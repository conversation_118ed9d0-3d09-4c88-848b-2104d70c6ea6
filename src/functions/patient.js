const { app } = require('@azure/functions')
const patientHandler = require('../handlers/patient-handler')
const { HttpStatusCode } = require('axios')
const { jsonResponse } = require('../common/helper')
const { HttpMethod } = require('../common/constant')

app.http('patient', {
  methods: ['GET', 'POST', 'PUT', 'PATCH'],
  authLevel: 'function',
  handler: async (req, context) => {
    context.log(`Http function processed request for url "${req.url}"`)
    const decode = context.extraInputs.get('decode')

    switch (req.method) {
      case HttpMethod.get:
        const id = req.query.get('id')
        const last_consultation_date = req.query.get('last_consultation_date')
        if (!id) {
          return jsonResponse(
            `Missing PatientId id=?`,
            HttpStatusCode.BadRequest,
          )
        }
        if (last_consultation_date) {
          var result = await patientHandler.GetPatientByQuery(
            `SELECT * FROM c WHERE c.id = '${id}' AND c.last_consultation_date = '${last_consultation_date}'`,
            decode.oid,
          )
          if (result.length > 0) {
            return jsonResponse(result[0])
          }
          return result
        }
        var data = await patientHandler.GetPatientProfile(id, decode.oid)
        return jsonResponse(data)

      case HttpMethod.post:
      case HttpMethod.put:
        if (!req.body) {
          return jsonResponse(
            `Missing Patient payload`,
            HttpStatusCode.BadRequest,
          )
        }
        const patient = await req.json()
        var data

        switch (req.method) {
          case HttpMethod.post:
            data = await patientHandler.CreatePatientProfile(
              patient,
              decode.oid,
            )
            if (!data) {
              return jsonResponse(
                `Unable to create Patient`,
                HttpStatusCode.Forbidden,
              )
            }
            break

          case HttpMethod.put:
            data = await patientHandler.UpdatePatientProfile(
              patient,
              decode.oid,
            )
            if (!data) {
              return jsonResponse(
                `Unable to update Patient`,
                HttpStatusCode.Forbidden,
              )
            }
            break
        }
        return jsonResponse(data)
      case HttpMethod.patch:
        if (!req.body) {
          return jsonResponse(
            `Missing Patient payload`,
            HttpStatusCode.BadRequest,
          )
        }
        const patient_patch_data = await req.json()
        var patientId = req.query.get('id') || ''
        var data = await patientHandler.UpsertPatientProfile(
          patientId,
          patient_patch_data,
          decode.oid,
        )
        return jsonResponse(data)
      default:
        return jsonResponse(
          `Unsupported HTTP method`,
          HttpStatusCode.MethodNotAllowed,
        )
    }
  },
})

app.http('patient-search', {
  methods: ['POST'],
  route: 'patient/search',
  authLevel: 'function',
  handler: async (req, context) => {
    const decode = context.extraInputs.get('decode')
    switch (req.method) {
      case HttpMethod.post:
        if (!req.body) {
          return jsonResponse(
            `Missing Patient query payload`,
            HttpStatusCode.BadRequest,
          )
        }
        const body = await req.json()
        var query = body?.query || ''
        var pageSize = body?.pagesize || 10
        var continuationToken = body?.continuetoken
        if (!query || query == '') {
          return jsonResponse(`Missing query`, HttpStatusCode.BadRequest)
        }
        var data = await patientHandler.SearchPatientProfiles(
          query,
          pageSize,
          continuationToken,
          decode.oid,
        )
        return jsonResponse(data)
      default:
        return jsonResponse(
          `Unsupported HTTP method`,
          HttpStatusCode.MethodNotAllowed,
        )
    }
  },
})

app.http('organization-patients', {
  methods: ['GET'],
  route: 'organization/patients',
  authLevel: 'function',
  handler: async (req, context) => {
    try {
      return await patientHandler.fetchPatientsForOrganization(req)
    } catch (err) {
      context.log.error('Error fetching organization patients:', err)
      return jsonResponse(
        'Error fetching organization patients',
        HttpStatusCode.InternalServerError,
      )
    }
  },
})
