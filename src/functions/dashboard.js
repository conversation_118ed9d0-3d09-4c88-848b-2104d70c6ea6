const { app } = require('@azure/functions')
const dashboardHandler = require('../handlers/admin/dashboard-handler')
const { jsonResponse } = require('../common/helper')

app.http('dashboard-summary', {
  methods: ['GET'],
  route: 'dashboard/summary',
  authLevel: 'function',
  handler: async (req, context) => {
    try {
      const data = await dashboardHandler.getDashboardData(req)
      return jsonResponse(data, 200)
    } catch (err) {
      return jsonResponse('Error fetching dashboard summary', 500)
    }
  },
})
