const CosmosDbMetadata = require('../models/CosmosDb-Metadata-model')

class PrescriptionModel extends CosmosDbMetadata {
  constructor(data) {
    super(data)
    this.id = data.id || ''
    this.patientId = data.patientId || ''
    this.doctor = data.doctor || ''
    this.doctorEmail = data.doctorEmail || '' 
    this.medicines = (data.medicines || []).map((med) => this.mapMedicine(med))
  }

  mapMedicine(med) {
    return {
      id: med.id || '',
      drugForm: med.drugForm || '',
      genericName: med.genericName || '',
      brandName: med.brandName || '',
      strength: med.strength || '',
      measure: med.measure || '',
      uom: med.uom || '',
      unit: med.unit || '',
      frequency: med.frequency || '',
      duration: med.duration || '',
      durationType: med.durationType || '',
      quantity: med.quantity || '',
      route: med.route || '',
      instructions: med.instructions || '',
      cost: med.cost || '',
      canSubstitute:
        typeof med.canSubstitute === 'boolean' ? med.canSubstitute : false,
    }
  }
}

module.exports = PrescriptionModel
