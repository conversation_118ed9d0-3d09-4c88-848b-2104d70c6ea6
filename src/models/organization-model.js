const CosmosDbMetadata = require('../models/CosmosDb-Metadata-model')

class OrganizationModel extends CosmosDbMetadata {
  constructor(data) {
    super(data)
    this.id = data.id || ''
    this.name = data.name || ''
    this.contactEmail = data.contactEmail || ''
    this.contactPersonName = data.contactPersonName || ''
    this.contactPhone = data.contactPhone || ''
    this.address = data.address || {}
    this.description = data.description || ''
    this.isActive = data.isActive !== undefined ? data.isActive : true
    this.createdAt = data.createdAt || new Date().toISOString() 
    this.updatedAt = data.updatedAt || new Date().toISOString()

    // Validate required fields
    if (!this.name) {
      throw new Error('Organization name is required.')
    }
    if (!this.contactEmail) {
      throw new Error('Contact email is required.')
    }
    if (!this.contactPersonName) {
      throw new Error('Contact person name is required.')
    }
  }
}

module.exports = OrganizationModel
