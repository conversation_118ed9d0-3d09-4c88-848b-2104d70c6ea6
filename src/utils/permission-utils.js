const { APIPermissions } = require('../common/permissions') // adjust path as needed

/**
 * Build API permission objects for a role from a list of permission keys
 * @param {string[]} permissionKeys - List of permission keys
 * @returns {Array} - Array of permission records to be saved
 */
function buildRoleAPIs(permissionKeys) {
  const roleAPIs = []

  for (const key of permissionKeys) {
    const permission = APIPermissions.find((p) => p.key === key)

    if (!permission) continue

    if (!permission.apis.length && !permission.methods.length) {
      roleAPIs.push({ permissionKey: key })
    } else {
      for (const api of permission.apis) {
        roleAPIs.push({
          api,
          methods: permission.methods,
          permissionKey: key,
        })
      }
    }
  }

  return roleAPIs
}
const organizationSuperAdminPermissionKeys = [
  // MRD Module Access & Permissions
  'mrd.access',
  'mrd.manage-patient.view',
  'mrd.manage-patient.edit',
  'mrd.patient-queue.manage',

  // EMR Module Access & Permissions
  'emr.access',
  'emr.patientinfo.view',
  'emr.patientinfo.edit',
  'emr.consultation.manage',
  'emr.prescription.view',
  'emr.prescription.manage',
  'emr.medicine-package.manage',
  'emr.reports.manage',
  'emr.test-package.manage',
  'emr.doctorprofile.view',
  'emr.doctorprofile.edit',
  'emr.lab-test.view',
  'emr.lab-test.manage',
  'emr.lab-test.search',
  'emr.test-package.view',
  'emr.prescription-package.view',
  'emr.lifestyle.manage',

  // Admin Portal Permissions
  'role.manage',
  'permission.manage',
  'organization.patients.view',
  'user.view',
  'user.manage',
  'dashboard.view',
  'organization.manage',
]
const nonAdminSystemRolePermissionKeys = [
  'emr.lab-test.view',
  'emr.lab-test.manage',
  'emr.lab-test.search',
  'emr.test-package.view',
  'emr.prescription-package.view',
  'role.manage',
  'permission.manage',
  'organization.manage',
  'organization.patients.view',
  'dashboard.view',
  'user.view',
  'user.manage',
  'emr.lifestyle.manage',
]

module.exports = {
  buildRoleAPIs,
  organizationSuperAdminPermissionKeys,
  nonAdminSystemRolePermissionKeys,
}
