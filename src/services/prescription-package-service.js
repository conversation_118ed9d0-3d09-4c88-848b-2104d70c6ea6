const packageRepository = require('../repositories/prescription-package-repository')
const logging = require('../common/logging')
const PackageModel = require('../models/package-model')
const medicineService = require('./medicine-service')
const { PackageType } = require('../common/constant')

class PackageService {
  async createPackage(packageData) {
    try {
      const existingPackages = await packageRepository.getPackageByName(
        packageData.name,
      )

      if (existingPackages.length > 0) {
        return {
          error: true,
          message: 'Package with the same name already exists',
        }
      }

      const newPackage = new PackageModel(packageData)
      const result = await packageRepository.createPackage(newPackage)
      return result
    } catch (error) {
      logging.logError('Failed to create package', error)
      throw error
    }
  }

  async getPrescriptionPackages(type, userId = null) {
    try {
      let result = []

      if (type === PackageType.USER && userId) {
        result = await packageRepository.getPackagesByTypeAndUser(type, userId)
      } else if (type === PackageType.DEPARTMENT) {
        result = await packageRepository.getPackagesByType(type)
      }

      return result
    } catch (error) {
      logging.logError(
        `Failed to fetch prescription packages of type ${type}`,
        error,
      )
      return {
        success: false,
        data: [],
        meta: { total: 0, type: type },
      }
    }
  }

  async getPrescriptionPackageById(packageId) {
    try {
      const result = await packageRepository.getPackageWithMedicines(packageId)

      if (!result || result.length === 0) {
        return {
          success: false,
          error: {
            code: 'PRESCRIPTION_PACKAGE_NOT_FOUND',
            message: 'Prescription package not found',
          },
        }
      }

      const packageData = result[0]

      const medicines = packageData.medicines.map((med) => ({
        id: med.id || med.productId,
        drugForm: med.drugForm || med.DrugFormulation,
        medicineName: med.medicineName || med.GenericName,
        brandName: med.brandName || med.BrandName,
        strength: med.strength || med.Strength,
        measure: med.measure || med.Measure,
        unitOfMeasure: med.unitOfMeasure || med.UnitOfMeasure,
        cost: med.cost || med.Cost,
        productId: med.productId || med.id,
      }))

      return {
        success: true,
        data: {
          id: packageData.id,
          type: packageData.type,
          description: packageData.description,
          medicineCount: packageData.medicines ? medicines.length : 0,
          name: packageData.name,
          medicines: medicines || [],
        },
      }
    } catch (error) {
      logging.logError(
        `Failed to fetch prescription package ${packageId}`,
        error,
      )
      return {
        success: false,
        error: { code: 'INTERNAL_ERROR', message: 'Server internal error' },
      }
    }
  }

  async createPrescriptionPackage(packageData, userId) {
    try {
      const { name, type, description, medicineIds } = packageData

      if (medicineIds && medicineIds.length > 50) {
        return {
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Maximum 50 medicines per package',
          },
        }
      }

      let existingPackages = []
      if (type === PackageType.USER) {
        existingPackages = await packageRepository.getPackageByNameAndUser(
          name,
          userId,
          type,
        )
      } else {
        existingPackages = await packageRepository.getPackageByNameAndType(
          name,
          type,
        )
      }

      if (existingPackages.length > 0) {
        return {
          success: false,
          error: {
            code: 'DUPLICATE_PACKAGE_NAME',
            message: 'Package name already exists',
          },
        }
      }

      let medicines = []
      if (medicineIds && medicineIds.length > 0) {
        let validMedicines = await medicineService.getMedicinesByIds(
          medicineIds,
        )

        if (validMedicines.length !== medicineIds.length) {
          const foundIds = validMedicines.map((m) => m.id)
          const missingIds = medicineIds.filter((id) => !foundIds.includes(id))

          return {
            success: false,
            error: {
              code: 'MEDICINE_NOT_FOUND',
              message: `Medicines not found: ${missingIds.join(', ')}`,
            },
          }
        }

        medicines = validMedicines.map((med) => {
          let measure = med.qty
          let unitOfMeasure = 'Nos'

          if (typeof measure === 'string' && measure.match(/[a-zA-Z]/)) {
            const match = measure.match(/^(\d+)\s*([a-zA-Z]+)$/)
            if (match) {
              measure = match[1]
              unitOfMeasure = match[2]
            }
          } else if (!isNaN(measure)) {
            unitOfMeasure = 'Nos'
          }

          return {
            id: med.id,
            productId: med.productId,
            medicineId: med.productId || med.id,
            medicineName: med.productName,
            brandName: med.productName,
            strength: med.qty,
            drugForm: med.productForm,
            genericName: med.saltComposition,
            manufacturer: med.marketerOrManufacturer,
            measure: med.qty,
            unitOfMeasure: unitOfMeasure,
            cost: med.mrp,
            addedAt: new Date().toISOString(),
            addedBy: userId,
            isActive: true,
          }
        })
      }

      const newPackageData = {
        name,
        type,
        description: description || '',
        medicines,
        createdBy: userId,
        isActive: true,
        usageStatistics: {
          timesUsed: 0,
          lastUsed: null,
          popularMedicines: [],
        },
      }

      const newPackage = new PackageModel(newPackageData)
      const result = await packageRepository.createPackage(newPackage)

      return {
        success: true,
        data: {
          id: result.id,
          name: result.name,
          type: result.type,
          createdBy: result.createdBy,
          description: result.description,
          isActive: result.isActive,
          createdAt: result.created_on,
          medicineCount: medicines.length,
        },
        message: 'Prescription package created successfully',
      }
    } catch (error) {
      logging.logError('Failed to create prescription package', error)
      return {
        success: false,
        error: { code: 'INTERNAL_ERROR', message: 'Server internal error' },
      }
    }
  }

  async updatePrescriptionPackage(packageId, updateData, userId) {
    try {
      const { name, medicineIds } = updateData

      if (medicineIds && medicineIds.length > 50) {
        return {
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Maximum 50 medicines per package',
          },
        }
      }

      const packageDoc = await packageRepository.getPackageById(packageId)
      if (!packageDoc || packageDoc.length === 0) {
        return {
          success: false,
          error: {
            code: 'PRESCRIPTION_PACKAGE_NOT_FOUND',
            message: 'Prescription package not found',
          },
        }
      }

      const existingPackage = packageDoc[0]

      if (
        existingPackage.type === PackageType.USER &&
        existingPackage.createdBy !== userId
      ) {
        return {
          success: false,
          error: {
            code: 'INSUFFICIENT_PERMISSIONS',
            message: 'Insufficient permissions to modify this package',
          },
        }
      }

      if (name && name !== existingPackage.name) {
        let existingPackages = []
        if (existingPackage.type === PackageType.USER) {
          existingPackages = await packageRepository.getPackageByNameAndUser(
            name,
            userId,
            existingPackage.type,
          )
        } else if (
          existingPackage.type === PackageType.DEPARTMENT &&
          existingPackage.departmentId
        ) {
          existingPackages =
            await packageRepository.getPackageByNameAndDepartment(
              name,
              existingPackage.departmentId,
              existingPackage.type,
            )
        } else {
          existingPackages = await packageRepository.getPackageByName(name)
        }

        if (existingPackages.length > 0) {
          return {
            success: false,
            error: {
              code: 'DUPLICATE_PACKAGE_NAME',
              message: 'Package name already exists',
            },
          }
        }
      }

      let newMedicines = []
      if (medicineIds && medicineIds.length > 0) {
        let validMedicines = []
        validMedicines = await medicineService.getMedicinesByIds(medicineIds)

        if (validMedicines.length < medicineIds.length) {
          const missingIds = medicineIds.filter(
            (id) => !foundProductIds.includes(id),
          )

          if (missingIds.length > 0) {
            validMedicines = [...validMedicines]
          }
        }

        if (validMedicines.length !== medicineIds.length) {
          const foundIds = validMedicines.map((m) => m.id)
          const missingIds = medicineIds.filter((id) => !foundIds.includes(id))

          return {
            success: false,
            error: {
              code: 'MEDICINE_NOT_FOUND',
              message: `Medicines not found: ${missingIds.join(', ')}`,
            },
          }
        }

        // Transform medicines to the required format (no standard protocols)
        newMedicines = validMedicines.map((med) => {
          return {
            id: med.id,
            productId: med.productId,
            medicineId: med.productId || med.id,
            medicineName: med.productName,
            brandName: med.productName,
            strength: med.qty,
            drugForm: med.productForm,
            genericName: med.saltComposition,
            manufacturer: med.marketerOrManufacturer,
            cost: med.mrp,
            addedAt: new Date().toISOString(),
            addedBy: userId,
            isActive: true,
          }
        })
      }

      // Calculate added and removed medicines (only if medicineIds provided)
      let addedMedicines = []
      let removedMedicines = []

      if (medicineIds) {
        const oldMedicineIds = (existingPackage.medicines || []).map(
          (m) => m.medicineId || m.productId || m.id,
        )
        const newMedicineIds = medicineIds || []

        addedMedicines = newMedicineIds.filter(
          (id) => !oldMedicineIds.includes(id),
        )
        removedMedicines = oldMedicineIds.filter(
          (id) => !newMedicineIds.includes(id),
        )

        // Update medicines only if medicineIds provided
        existingPackage.medicines = newMedicines
      }

      // Update package name if provided
      if (name && name !== existingPackage.name) {
        existingPackage.name = name
      }

      existingPackage.updated_on = new Date().toISOString()
      existingPackage.updated_by = userId

      await packageRepository.upsertPackage(existingPackage)

      return {
        success: true,
        data: {
          id: existingPackage.id,
          name: existingPackage.name,
          type: existingPackage.type,
          updatedAt: existingPackage.updated_on,
          medicineCount: existingPackage.medicines
            ? existingPackage.medicines.length
            : 0,
          addedMedicines,
          removedMedicines,
        },
        message: 'Prescription package updated successfully',
      }
    } catch (error) {
      logging.logError(
        `Failed to update prescription package ${packageId}`,
        error,
      )
      return {
        success: false,
        error: { code: 'INTERNAL_ERROR', message: 'Server internal error' },
      }
    }
  }

  async deletePrescriptionPackage(packageId, userId) {
    try {
      const packageDoc = await packageRepository.getPackageById(packageId)
      if (!packageDoc || packageDoc.length === 0) {
        return {
          success: false,
          error: {
            code: 'PRESCRIPTION_PACKAGE_NOT_FOUND',
            message: 'Prescription package not found',
          },
        }
      }

      const existingPackage = packageDoc[0]

      if (
        existingPackage.type === PackageType.USER &&
        existingPackage.createdBy !== userId
      ) {
        return {
          success: false,
          error: {
            code: 'INSUFFICIENT_PERMISSIONS',
            message: 'Insufficient permissions to delete this package',
          },
        }
      }

      await packageRepository.deletePackage(packageId)

      return {
        success: true,
        data: {
          id: packageId,
          deletedAt: new Date().toISOString(),
          deletedBy: userId,
        },
        message: 'Prescription package deleted successfully',
      }
    } catch (error) {
      logging.logError(
        `Failed to delete prescription package ${packageId}`,
        error,
      )
      return {
        success: false,
        error: { code: 'INTERNAL_ERROR', message: 'Server internal error' },
      }
    }
  }
}

module.exports = new PackageService()
