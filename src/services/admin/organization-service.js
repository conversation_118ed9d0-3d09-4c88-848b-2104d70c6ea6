const { logger } = require('@azure/identity')
const organizationRepository = require('../../repositories/admin/organization-repository')
const userRepository = require('../../repositories/admin/user-repository')
const bcrypt = require('bcrypt')
const { DefaultRoles } = require('../../common/roles')
const OrganizationModel = require('../../models/organization-model')
const roleRepository = require('../../repositories/admin/role-repository')
const rolePermissionService = require('../role-permission-service')
const userService = require('../user-service')
const auditLogger = require('../../common/audit-logger')
const { logInfo, logError } = require('../../common/logging')
const { generateSecurePassword } = require('../../utils/password-utils')
const b2cService = require('../b2c-service')
const { APIPermissions } = require('../../common/permissions')
const graphService = require('../graph-service')
const NodeCache = require('node-cache')
const {
  organizationSuperAdminPermissionKeys,
  nonAdminSystemRolePermissionKeys,
} = require('../../utils/permission-utils')

class OrganizationService {
  async seedSuperAdmin(superAdmin) {
    logger.info('Checking if Super Admin already exists...')
    const existingSuperAdmin = await userRepository.getUserByRole(
      DefaultRoles.SUPER_ADMIN,
    )

    if (existingSuperAdmin && existingSuperAdmin.length > 0) {
      logger.info('Super Admin already exists. Skipping seeding.')
      return
    }

    logger.info('Seeding Super Admin user...')
    const hashedPassword = await bcrypt.hash(superAdmin.password, 10)
    await userRepository.createSuperAdmin({
      ...superAdmin,
      password: hashedPassword,
    })
    logger.info('Super Admin created successfully.')
  }

  async createOrganization(data) {
    const organizationData = new OrganizationModel(data)
    const organization = await organizationRepository.createOrganization(
      organizationData,
    )

    // Create admin user object for local database (without password fields)
    const adminUserObj = {
      userRole: DefaultRoles.ORGANIZATION_SUPER_ADMIN,
      name: organizationData.contactPersonName,
      email: organizationData.contactEmail,
      isActive: true,
      userType: DefaultRoles.ORGANIZATION_SUPER_ADMIN,
      organizationId: organization.id,
      isOrganizationMainAdmin: true,
      created_by: 'system',
      updated_by: 'system',
    }

    // Remove password-related fields as they're handled by B2C
    delete adminUserObj.password
    delete adminUserObj.resetToken
    delete adminUserObj.resetTokenExpiry

    try {
      const localUser = await userService.addUser(adminUserObj)

      const temporaryPassword = generateSecurePassword()

      const b2cUser = {
        accountEnabled: true, // User is active but must change password
        displayName: organizationData.contactPersonName,
        identities: [
          {
            signInType: 'emailAddress',
            issuer: `${process.env.TENANT_NAME}.onmicrosoft.com`,
            issuerAssignedId: organizationData.contactEmail,
          },
        ],
        passwordProfile: {
          forceChangePasswordNextSignIn: true, // Force password change on first login
          password: temporaryPassword,
        },
        passwordPolicies: 'DisablePasswordExpiration, DisableStrongPassword',
      }

      const b2cResult = await b2cService.createB2CUser(b2cUser)
      logInfo(
        `B2C admin user created successfully for organization: ${organizationData.name}`,
      )

      // Update local user with B2C user ID
      localUser.b2cUserId = b2cResult.id
      await userService.updateUser(localUser)

      // Send welcome email with temporary password and B2C OAuth login link
      await b2cService.sendWelcomeEmailWithB2CSetup(
        organizationData.contactEmail,
        organizationData.contactPersonName,
        temporaryPassword,
        true, // isAdmin = true for organization admin creation
      )

      await auditLogger.logAction(
        'Organization Created with Admin B2C Integration',
        'system',
        {
          organizationId: organization.id,
          adminUserId: localUser.id,
          b2cUserId: b2cResult.id,
        },
      )

      logInfo(
        `Organization creation completed successfully: ${organizationData.name}`,
      )

      // Create default roles and assign Organization Super Admin role to the user
      const defaultRoles = Object.keys(DefaultRoles)
        .filter((key) => key !== 'SUPER_ADMIN')
        .map((key) => ({
          id: `${organization.id}-${key}`,
          name: DefaultRoles[key],
          organizationId: organization.id,
          isDefault: true,
        }))
      // Convert permission keys to API format
      const convertPermissionsToAPIFormat = (permissionKeys) => {
        return permissionKeys.flatMap((permissionKey) => {
          const permission = APIPermissions.find((p) => p.key === permissionKey)

          if (!permission) {
            logError(`Permission not found: ${permissionKey}`)
            return []
          }

          if (permission.apis.length === 0 && permission.methods.length === 0) {
            return [{ permissionKey }]
          }

          return permission.apis.map((api) => ({
            api,
            methods: permission.methods,
            permissionKey,
          }))
        })
      }

      let organizationSuperAdminRoleId = null

      for (const role of defaultRoles) {
        await roleRepository.createRole(role)

        // Prepare permissions based on role type
        let rolePermissions = []
        if (role.name === DefaultRoles.ORGANIZATION_SUPER_ADMIN) {
          organizationSuperAdminRoleId = role.id
          rolePermissions = convertPermissionsToAPIFormat(
            organizationSuperAdminPermissionKeys,
          )
        } else {
          rolePermissions = convertPermissionsToAPIFormat(
            nonAdminSystemRolePermissionKeys,
          )
        }
        console.log(rolePermissions, 'rolePermissions')

        const rolePermissionRecord = {
          id: role.id,
          roleName: role.name,
          organizationId: role.organizationId,
          APIs: rolePermissions,
          created_on: new Date().toISOString(),
          updated_on: new Date().toISOString(),
        }
        await rolePermissionService.addRolePermission(rolePermissionRecord)
      }

      // Update the admin user with the Organization Super Admin role ID
      if (organizationSuperAdminRoleId && localUser) {
        localUser.roleId = organizationSuperAdminRoleId
        await userService.updateUser(localUser)
        logInfo(
          `Organization Super Admin role assigned to user: ${organizationData.contactEmail}`,
        )
      }
    } catch (error) {
      logError(
        `Organization admin creation failed for: ${organizationData.contactEmail}`,
        error,
      )

      // Preserve GraphError and Microsoft Graph API error properties for proper handling upstream
      if (error.name === 'GraphError' || error.code === 'Request_BadRequest') {
        throw error // Re-throw error as-is
      }

      // For other errors, wrap them but preserve important properties
      const wrappedError = new Error(
        `Failed to create organization admin: ${error.message}`,
      )
      wrappedError.code = error.code || 'ORGANIZATION_ADMIN_CREATION_FAILED'
      wrappedError.statusCode = error.statusCode || 500
      wrappedError.originalError = error
      throw wrappedError
    }

    return { message: 'Organization and Admin created successfully' }
  }

  async editOrganization(data) {
    try {
      const existingOrganization =
        await organizationRepository.getOrganizationById(data.id)
      if (!existingOrganization) {
        throw new Error('Organization not found')
      }

      const organizationData = new OrganizationModel(data)
      if (data.status) {
        organizationData.status = data.status
      }

      if (
        existingOrganization.contactPersonName !==
        organizationData.contactPersonName
      ) {
        logInfo(
          `Contact person name changed from "${existingOrganization.contactPersonName}" to "${organizationData.contactPersonName}" for organization ${data.id}`,
        )

        try {
          const users = await userService.getUserByEmail(
            existingOrganization.contactEmail,
          )
          if (users && users.length > 0) {
            const user = users[0]

            user.name = organizationData.contactPersonName
            user.updated_on = new Date().toISOString()

            const a = await userService.updateUser(user)
            console.log(a)

            logInfo(
              `Updated user name in database for email: ${existingOrganization.contactEmail}`,
            )
            if (user.b2cUserId) {
              try {
                const cache = new NodeCache({ checkperiod: 600 })

                var Token = cache.get(`graphToken`)
                if (
                  !Token ||
                  Token.expiresOn.getTime() < new Date().getTime()
                ) {
                  Token = await graphService.getToken()
                  cache.set('graphToken', {
                    accessToken: Token.accessToken,
                    expiresOn: Token.expiresOn,
                  })
                }

                await graphService.updateUser(
                  Token.accessToken,
                  user.b2cUserId,
                  {
                    name: organizationData.contactPersonName,
                  },
                )

                logInfo(`Updated B2C displayName for user: ${user.b2cUserId}`)
              } catch (b2cError) {
                logError(
                  `Failed to update B2C displayName for user ${user.b2cUserId}:`,
                  b2cError,
                )
                // Don't fail the organization update if B2C update fails
              }
            }
          } else {
            logInfo(
              `No user found with email: ${existingOrganization.contactEmail}`,
            )
          }
        } catch (userUpdateError) {
          logError(
            `Failed to update user for contact person name change:`,
            userUpdateError,
          )
          // Don't fail the organization update if user update fails
        }
      }

      return organizationRepository.updateOrganization(organizationData)
    } catch (error) {
      logError('Error editing organization:', error)
      throw error
    }
  }

  async listOrganizations(nameFilter, pageSize, pageNumber) {
    const result = await organizationRepository.getAllOrganizations(
      nameFilter,
      pageSize,
      pageNumber,
    )

    return {
      organizations: result.items,
      totalCount: result.totalCount,
      totalPages: result.totalPages,
      currentPage: pageNumber,
    }
  }

  async selectOrganization(superAdminId, organizationId) {
    const organization = await organizationRepository.getOrganizationById(
      organizationId,
    )
    if (!organization || !organization.isActive) {
      throw new Error('Invalid or inactive organization')
    }
    await userRepository.updateUserContext(superAdminId, { organizationId })
    return organization
  }

  async getOrganizationById(id) {
    return organizationRepository.getOrganizationById(id)
  }

  async deactivateOrganizationUsers(organizationId) {
    const users = await userRepository.getUsersByOrganizationId(organizationId)
    for (const user of users) {
      user.isActive = false
      await userRepository.updateUser(user.id, user)
    }
  }

  async checkOrganizationLinkedToUsers(organizationId) {
    const users = await userRepository.getUsersByOrganizationId(organizationId)
    return users.length > 0
  }

  async deleteOrganization(organizationId) {
    return await organizationRepository.deleteOrganization(organizationId)
  }
}

module.exports = new OrganizationService()
