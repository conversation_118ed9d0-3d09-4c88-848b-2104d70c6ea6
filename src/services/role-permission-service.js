const logging = require('../common/logging')
const cosmosDbContext = require('../cosmosDbContext/comosdb-context')

const containerRolePermission = 'sys_role_permissions'

class RolePermissionService {
  async getAPIsListbyRole(roleName) {
    try {
      const query = `SELECT * FROM c WHERE c.roleName = '${roleName}'`
      const data = await cosmosDbContext.queryItems(
        query,
        containerRolePermission,
      )
      return data
    } catch (error) {
      logging.logError(`Unable to get APIs list by role :: ${roleName}`, error)
      return null
    }
  }

  async getRoleById(roleId) {
    try {
      const query = `SELECT * FROM c WHERE c.id = '${roleId}'`
      const data = await cosmosDbContext.queryItems(
        query,
        containerRolePermission,
      )
      return data.length > 0 ? data[0] : null
    } catch (error) {
      logging.logError(`Unable to get role by ID :: ${roleId}`, error)
      return null
    }
  }

  async getRoleByIdAndOrganization(roleId, organizationId) {
    try {
      const query = `SELECT * FROM c WHERE c.id = '${roleId}' AND c.organizationId = '${organizationId}'`
      const data = await cosmosDbContext.queryItems(
        query,
        containerRolePermission,
      )
      return data.length > 0 ? data[0] : null
    } catch (error) {
      logging.logError(
        `Unable to get role by ID and organization :: ${roleId}, ${organizationId}`,
        error,
      )
      return null
    }
  }

  async addRolePermission(rolePermission) {
    try {
      const result = await cosmosDbContext.createItem(
        rolePermission,
        containerRolePermission,
      )
      return result
    } catch (error) {
      logging.logError(`Unable to create role permission`, error)
      return null
    }
  }

  async updateRolePermission(rolePermission) {
    try {
      const result = await cosmosDbContext.upsertItem(
        rolePermission.id,
        rolePermission,
        containerRolePermission,
      )
      return result
    } catch (error) {
      logging.logError(`Unable to update role permission`, error)
      return null
    }
  }
}

module.exports = new RolePermissionService()
