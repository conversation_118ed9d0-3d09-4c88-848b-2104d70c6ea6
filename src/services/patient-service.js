const logging = require('../common/logging')
const cosmosDbContext = require('../cosmosDbContext/comosdb-context')
const PatientModel = require('../models/patient-model')
const patientRepository = require('../repositories/patient-repository')

const patientProfileContainer = 'PatientProfiles'
const patientHistoryContainer = 'PatientHistory'
const patientConsultationsContainer = 'PatientConsultations'
const patientMedicationsContainer = 'PatientMedications'
const patientReportsContainer = 'PatientReport'
const patientConsultantContainer = 'PatientConsultings'
const patientLifeStyleContainer = 'PatientLifeStyles'
const patientDiagnosisNotesContainer = 'PatientDiagnosisNotes'
const patientLifeStyleNoteContainer = 'PatientLifeStyleNotes'
class PatientService {
  async CreatePatientProfile(patient) {
    try {
      var res = await cosmosDbContext.createItem(
        patient,
        patientProfileContainer,
      )
      return res
    } catch (error) {
      logging.logError(`Unable to create Patient`, error)
      return null
    }
  }

  async UpdatePatientProfile(patient) {
    try {
      var res = await cosmosDbContext.updateItem(
        patient,
        patientProfileContainer,
      )
      return res
    } catch (error) {
      logging.logError(`Unable to update patient`, error)
      return null
    }
  }

  async GetPatientProfiles(pageSize, continueToken) {
    try {
      var data = await cosmosDbContext.getAllItems(
        patientProfileContainer,
        pageSize,
        continueToken,
      )
      return data
    } catch (error) {
      logging.logError(`Unable to get patient profiles`, error)
      return null
    }
  }

  async GetPatientProfilesByOrganization(
    organizationId,
    pageSize,
    continueToken,
  ) {
    try {
      const query = `SELECT * FROM c WHERE c.organizationId = "${organizationId}"`
      var data = await cosmosDbContext.getAllItemQuery(
        patientProfileContainer,
        query,
        pageSize,
        continueToken,
      )
      return data
    } catch (error) {
      logging.logError(
        `Unable to get patient profiles for organization ${organizationId}`,
        error,
      )
      return null
    }
  }

  async SearchPatient(queryString, pageSize, continuationToken) {
    try {
      var data = await cosmosDbContext.getAllItemQuery(
        patientProfileContainer,
        queryString,
        pageSize,
        continuationToken,
      )

      if (data && data.items && data.items.length > 0) {
        const searchText = this.extractSearchTextFromQuery(queryString)
        if (searchText) {
          data.items = this.sortPatientSearchResults(data.items, searchText)
        }
      }

      return data
    } catch (error) {
      logging.logError(
        `Unable to get patient profiles with query :: ${queryString}`,
        error,
      )
      return null
    }
  }

  // Helper function to extract search text from SQL query
  extractSearchTextFromQuery(queryString) {
    try {
      // Look for CONTAINS patterns in the query
      const containsMatches = queryString.match(
        /CONTAINS\([^,]+,\s*["']([^"']+)["']\)/gi,
      )
      if (containsMatches && containsMatches.length > 0) {
        // Extract the search text from the first CONTAINS clause
        const match = containsMatches[0].match(
          /CONTAINS\([^,]+,\s*["']([^"']+)["']\)/i,
        )
        return match ? match[1] : null
      }
      return null
    } catch (error) {
      console.log('Error extracting search text from query:', error)
      return null
    }
  }

  // Sort patient search results by relevance to search text
  sortPatientSearchResults(items, searchText) {
    if (!searchText || !items || items.length === 0) return items

    const lowerSearch = searchText.toLowerCase()

    return items.sort((a, b) => {
      const aName = a.name?.toLowerCase() || ''
      const bName = b.name?.toLowerCase() || ''
      const aId = a.id?.toLowerCase() || ''
      const bId = b.id?.toLowerCase() || ''
      const aPhone = a.contact?.phone?.toLowerCase() || ''
      const bPhone = b.contact?.phone?.toLowerCase() || ''

      // Priority 1: Exact name match
      const aExactName = aName === lowerSearch ? 0 : 1
      const bExactName = bName === lowerSearch ? 0 : 1
      if (aExactName !== bExactName) return aExactName - bExactName

      // Priority 2: Name starts with search text
      const aNameStarts = aName.startsWith(lowerSearch) ? 0 : 1
      const bNameStarts = bName.startsWith(lowerSearch) ? 0 : 1
      if (aNameStarts !== bNameStarts) return aNameStarts - bNameStarts

      // Priority 3: ID starts with search text
      const aIdStarts = aId.startsWith(lowerSearch) ? 0 : 1
      const bIdStarts = bId.startsWith(lowerSearch) ? 0 : 1
      if (aIdStarts !== bIdStarts) return aIdStarts - bIdStarts

      // Priority 4: Phone starts with search text
      const aPhoneStarts = aPhone.startsWith(lowerSearch) ? 0 : 1
      const bPhoneStarts = bPhone.startsWith(lowerSearch) ? 0 : 1
      if (aPhoneStarts !== bPhoneStarts) return aPhoneStarts - bPhoneStarts

      // Priority 5: Name contains search text (but doesn't start with it)
      const aNameContains = aName.includes(lowerSearch) ? 0 : 1
      const bNameContains = bName.includes(lowerSearch) ? 0 : 1
      if (aNameContains !== bNameContains) return aNameContains - bNameContains

      // Priority 6: ID contains search text
      const aIdContains = aId.includes(lowerSearch) ? 0 : 1
      const bIdContains = bId.includes(lowerSearch) ? 0 : 1
      if (aIdContains !== bIdContains) return aIdContains - bIdContains

      // Priority 7: Phone contains search text
      const aPhoneContains = aPhone.includes(lowerSearch) ? 0 : 1
      const bPhoneContains = bPhone.includes(lowerSearch) ? 0 : 1
      if (aPhoneContains !== bPhoneContains)
        return aPhoneContains - bPhoneContains

      // Final fallback: Alphabetical order by name
      return aName.localeCompare(bName)
    })
  }

  async GetPatientProfile(patientId) {
    try {
      var data = await cosmosDbContext.readItem(
        patientId,
        patientId,
        patientProfileContainer,
      )
      if (data) {
        return new PatientModel(data)
      }
      return data
    } catch (error) {
      logging.logError(`Unable to get patient id ${patientId}`, error)
      return null
    }
  }

  async QueryPatientProfile(queryString) {
    try {
      var data = await cosmosDbContext.queryItems(
        queryString,
        patientProfileContainer,
      )
      return data
    } catch (error) {
      logging.logError(`Unable to get user by query ${queryString}`, error)
      return null
    }
  }

  async CreatePatientHistory(patientId, patientHistory) {
    try {
      patientHistory.patientId = patientId
      var res = await cosmosDbContext.createItem(
        patientHistory,
        patientHistoryContainer,
      )
      return res
    } catch (error) {
      logging.logError(
        `Unable to create patient history for patient ${patientId}`,
        error,
      )
      return null
    }
  }

  async updatePatientHistory(patientHistory) {
    try {
      var res = await cosmosDbContext.updateItem(
        patientHistory,
        patientHistoryContainer,
      )
      return res
    } catch (error) {
      logging.logError(
        `Unable to update patient history for patient ${patientHistory.patientId}`,
        error,
      )
      return null
    }
  }

  async GetPatientHistory(patientId) {
    try {
      var query = "SELECT * FROM c WHERE c.patientId= '" + patientId + "'"
      var res = await cosmosDbContext.queryItems(query, patientHistoryContainer)
      return res
    } catch (error) {
      logging.logError(
        `Unable to get patient history for patient ${patientId}`,
        error,
      )
      return null
    }
  }

  async getPatientHistoryByQuey(query) {
    try {
      var res = await cosmosDbContext.queryItems(query, patientHistoryContainer)
      return res
    } catch (error) {
      logging.logError(
        `Unable to get patient history by query :: ${query}`,
        error,
      )
      return null
    }
  }

  async CreatePatientConsultation(patientId, patientConsultation) {
    try {
      patientConsultation.patientId = patientId
      var res = await cosmosDbContext.createItem(
        patientConsultation,
        patientConsultationsContainer,
      )
      return res
    } catch (error) {
      logging.logError(
        `Unable to create patient consultations for patient ${patientId}`,
        error,
      )
      return null
    }
  }

  async UpdatePatientConsultations(patientConsultation) {
    try {
      var res = await cosmosDbContext.updateItem(
        patientConsultation,
        patientConsultationsContainer,
      )
      return res
    } catch (error) {
      logging.logError(
        `Unable to update patient Consultation for patient ${patientConsultation.patientId}`,
        error,
      )
      return null
    }
  }

  async GetPatientConsultations(patientId) {
    try {
      var query = "SELECT * FROM c WHERE c.patientId= '" + patientId + "'"
      var res = await cosmosDbContext.queryItems(
        query,
        patientConsultationsContainer,
      )
      return res
    } catch (error) {
      logging.logError(
        `Unable to get patient Consultations for patient ${patientId}`,
        error,
      )
      return null
    }
  }

  async GetPatientConsultant(patienId, date) {
    try {
      var query = `SELECT * FROM c WHERE c.patientId= '${patienId}' AND c.date= '${date}' `
      var res = await cosmosDbContext.queryItems(
        query,
        patientConsultantContainer,
      )
      return res
    } catch (error) {
      logging.logError(
        `Unable to get patient consultant with PatientId: ${patienId} and date: ${date}`,
      )
      return null
    }
  }

  async CreatePatientConsultant(patientId, patientConsultant) {
    try {
      patientConsultant.patienId = patientId
      var res = await cosmosDbContext.createItem(
        patientConsultant,
        patientConsultantContainer,
      )
      return res
    } catch (error) {
      logging.logError(
        `Unable to create patient consultant for patient ${patientId}`,
        error,
      )
      return null
    }
  }

  async UpdatePatientConsultant(patientConsultant) {
    try {
      var res = await cosmosDbContext.updateItem(
        patientConsultant,
        patientConsultationsContainer,
      )
      return res
    } catch (error) {
      logging.logError(ex)
      return null
    }
  }

  async UpsertPatient(patienId, payload) {
    try {
      var res = await cosmosDbContext.patchItem(
        patienId,
        payload,
        patientProfileContainer,
      )
      return res
    } catch (ex) {
      logging.logError(ex)
      return null
    }
  }

  async createPatientLifeStyle(patientId, lifeStyle) {
    try {
      lifeStyle.patientId = patientId
      var res = await cosmosDbContext.createItem(
        lifeStyle,
        patientLifeStyleContainer,
      )
      return res
    } catch (error) {
      logging.logError(
        `Unable to create patient lifestyle for patient ${patientId}`,
        error,
      )
      return null
    }
  }

  async getPatientLifeStyle(patientId) {
    try {
      var query = `SELECT * FROM c WHERE c.patientId= '${patientId}'`
      var res = await cosmosDbContext.queryItems(
        query,
        patientLifeStyleContainer,
      )
      return res
    } catch (error) {
      logging.logError(
        `Unable to get patient lifestyle for patient ${patientId}`,
        error,
      )
      return null
    }
  }

  async getPatientLifeStyleBySourceName(patientId, source) {
    try {
      var query = `SELECT * FROM c WHERE c.patientId= '${patientId}' AND c.source= '${source}'`
      var res = await cosmosDbContext.queryItems(
        query,
        patientLifeStyleContainer,
      )
      return res
    } catch (error) {
      logging.logError(
        `Unable to get patient lifestyle for patient ${patientId} and source ${source}`,
        error,
      )
      return null
    }
  }

  async patchPatientLifeStyle(id, lifeStyle) {
    try {
      var res = await cosmosDbContext.patchItem(
        id,
        lifeStyle,
        patientLifeStyleContainer,
      )
      return res
    } catch (error) {
      logging.logError(
        `Unable to patch patient lifestyle for patient ${lifeStyle.patientId}`,
        error,
      )
      return null
    }
  }

  async createPatientDiagnosisNotes(patientId, diagnosisNotes) {
    try {
      diagnosisNotes.patientId = patientId
      var res = await cosmosDbContext.createItem(
        diagnosisNotes,
        patientDiagnosisNotesContainer,
      )
      return res
    } catch (error) {
      logging.logError(
        `Unable to create patient diagnosis notes for patient ${patientId}`,
        error,
      )
      return null
    }
  }

  async getPatientDiagnosisNotes(patientId) {
    try {
      var query = `SELECT * FROM c WHERE c.patientId= '${patientId}'`
      var res = await cosmosDbContext.queryItems(
        query,
        patientDiagnosisNotesContainer,
      )
      return res
    } catch (error) {
      logging.logError(
        `Unable to get patient diagnosis notes for patient ${patientId}`,
        error,
      )
      return null
    }
  }

  async patchPatientDiagnosisNotes(id, diagnosisNotes) {
    try {
      var res = await cosmosDbContext.patchItem(
        id,
        diagnosisNotes,
        patientDiagnosisNotesContainer,
      )
      return res
    } catch (error) {
      logging.logError(
        `Unable to patch patient diagnosis notes for patient ${patientId}`,
        error,
      )
      return null
    }
  }

  async getPatientDiagnosisNotesByQuery(query) {
    try {
      var res = await cosmosDbContext.queryItems(
        query,
        patientDiagnosisNotesContainer,
      )
      return res
    } catch (error) {
      logging.logError(
        `Unable to get patient diagnosis notes by query ${query}`,
        error,
      )
      return null
    }
  }

  async getPatientLifeStyleBySourceNameAndSession(
    patientId,
    source,
    fromDate,
    toDate,
  ) {
    try {
      // Append time portion to dates
      const fromDateTime = `${fromDate}T00:00:00Z`
      const toDateTime = `${toDate}T23:59:59Z`

      const query = `
                SELECT * FROM c 
                WHERE c.patientId = '${patientId}' 
                  AND c.source = '${source}' 
                  AND c.updated_on >= '${fromDateTime}' 
                  AND c.updated_on <= '${toDateTime}'
            `

      var res = await cosmosDbContext.queryItems(
        query,
        patientLifeStyleContainer,
      )
      if (res && res.length == 0) {
        //try to get data again
        const query2 = `
                    SELECT * FROM c 
                    WHERE c.patientId = '${patientId}' 
                      AND c.source = '${source}' 
                      AND c.created_on >= '${fromDateTime}' 
                      AND c.created_on <= '${toDateTime}'
                `
        res = await cosmosDbContext.queryItems(
          query2,
          patientLifeStyleContainer,
        )
      }
      return res
    } catch (error) {
      logging.logError(
        `Unable to get patient lifestyle for patient ${patientId} and source ${source}`,
        error,
      )
      return null
    }
  }

  async createPatientLifeStyleNote(lifeStyleNote) {
    try {
      var res = await cosmosDbContext.createItem(
        lifeStyleNote,
        patientLifeStyleNoteContainer,
      )
      return res
    } catch (error) {
      logging.logError(
        `Unable to create patient lifestyle note for patient ${lifeStyleNote.patientId}`,
        error,
      )
      return null
    }
  }
  async getPatientLifeStyleNote(patientId) {
    try {
      var query = `SELECT * FROM c WHERE c.patientId= '${patientId}'`
      var res = await cosmosDbContext.queryItems(
        query,
        patientLifeStyleNoteContainer,
      )
      return res
    } catch (error) {
      logging.logError(
        `Unable to get patient lifestyle note for patient ${patientId}`,
        error,
      )
      return null
    }
  }
  async patchPatientLifeStyleNote(id, lifeStyleNote) {
    try {
      var res = await cosmosDbContext.patchItem(
        id,
        lifeStyleNote,
        patientLifeStyleNoteContainer,
      )
      return res
    } catch (error) {
      logging.logError(
        `Unable to patch patient lifestyle note for patient ${patientId}`,
        error,
      )
      return null
    }
  }
  async getPatientLifeStyleNoteByQuery(query) {
    try {
      var res = await cosmosDbContext.queryItems(
        query,
        patientLifeStyleNoteContainer,
      )
      return res
    } catch (error) {
      logging.logError(
        `Unable to get patient lifestyle note by query ${query}`,
        error,
      )
      return null
    }
  }
  async deletePatientLifeStyleNote(id) {
    try {
      var res = await cosmosDbContext.deleteItem(
        id,
        patientLifeStyleNoteContainer,
      )
      return res
    } catch (error) {
      logging.logError(
        `Unable to delete patient lifestyle note for patient ${patientId}`,
        error,
      )
      return null
    }
  }

  async getPatientLifeStyleByQuery(query) {
    try {
      return await cosmosDbContext.queryItems(query, patientLifeStyleContainer)
    } catch (error) {
      logging.logError(
        `Unable to query patient lifestyle with query ${query}`,
        error,
      )
      return null
    }
  }

  async fetchPatientsForOrganization(
    organizationId,
    searchText,
    filters,
    sortBy,
    sortOrder,
    pageSize,
    page,
  ) {
    try {
      return await patientRepository.fetchPatientsForOrganization(
        organizationId,
        searchText,
        filters,
        sortBy,
        sortOrder,
        pageSize,
        page,
      )
    } catch (error) {
      console.error('Error fetching patients for organization:', error)
      throw new Error('Failed to fetch patients for organization')
    }
  }
}
module.exports = new PatientService()
