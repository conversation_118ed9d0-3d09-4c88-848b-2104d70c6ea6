/**
 * Azure B2C Service for user management operations
 * Handles B2C user creation, authentication, and related operations
 */

const { logError, logInfo } = require('../common/logging')
const graphService = require('./graph-service')
const emailService = require('./email-service')
const NodeCache = require('node-cache')
const cache = new NodeCache({ checkperiod: 600 })
const crypto = require('crypto')

class B2CService {
  /**
   * Create a user in Azure B2C
   * @param {Object} user - User object with B2C properties
   * @returns {Object} - Created B2C user data
   */
  async createB2CUser(user) {
    try {
      var Token = cache.get(`graphToken`)
      if (!Token || Token.expiresOn.getTime() < new Date().getTime()) {
        Token = await graphService.getToken()
        cache.set('graphToken', {
          accessToken: Token.accessToken,
          expiresOn: Token.expiresOn,
        })
      }

      // Check if the user already exists in AAD
      const existingUser = await graphService.getUserByPrincipalName(
        Token.accessToken,
        user.identities[0].issuerAssignedId,
      )
      if (existingUser) {
        console.log(
          `User with email ${user.identities[0].issuerAssignedId} already exists in AAD`,
        )
        throw new Error(
          `User with email ${user.identities[0].issuerAssignedId} already exists in AAD`,
        )
      }

      // Create the user if it doesn't exist
      var data = await graphService.createUser(Token.accessToken, user)
      return data
    } catch (error) {
      // Handle GraphError specifically - preserve it for upstream handling
      if (error.name === 'GraphError') {
        logError(`GraphError creating B2C user:`, error)
        throw error // Re-throw GraphError as-is to preserve all properties
      }

      if (error.code === 'USER_ALREADY_EXISTS') {
        throw error
      }

      logError(`Error creating B2C user:`, error)
      throw error
    }
  }

  /**
   * Generate PKCE challenge for OAuth flow
   * @returns {Object} - Object containing codeVerifier and codeChallenge
   */
  generatePKCEChallenge() {
    // Generate code verifier (43-128 characters)
    const codeVerifier = crypto.randomBytes(32).toString('base64url')

    // Generate code challenge (SHA256 hash of verifier, base64url encoded)
    const codeChallenge = crypto
      .createHash('sha256')
      .update(codeVerifier)
      .digest('base64url')

    return { codeVerifier, codeChallenge }
  }

  /**
   * Send welcome email with B2C setup instructions
   * @param {string} email - User email address
   * @param {string} name - User display name
   * @param {string} temporaryPassword - Temporary password for first login
   * @param {boolean} isAdmin - Whether this is for admin (organization creation) or regular user
   */
  async sendWelcomeEmailWithB2CSetup(
    email,
    name,
    temporaryPassword,
    isAdmin = false,
  ) {
    try {
      const { codeChallenge } = this.generatePKCEChallenge()
      const nonce = `${Date.now()}-${crypto.randomBytes(8).toString('hex')}`
      const clientRequestId = `${Date.now()}-${crypto
        .randomBytes(8)
        .toString('hex')}`

      const state = {
        id: `${Date.now()}-${crypto.randomBytes(8).toString('hex')}`,
        meta: { interactionType: 'popup' },
      }

      // Determine redirect URI based on user type
      const redirectUri = isAdmin
        ? process.env.BASE_ADMIN_URL
        : process.env.BASE_URL

      const b2cLoginUrl =
        `https://${process.env.TENANT_NAME}.b2clogin.com/${process.env.TENANT_NAME}.onmicrosoft.com/${process.env.signin_policy}/oauth2/v2.0/authorize?` +
        `client_id=${process.env.CLIENT_ID}&` +
        `scope=${encodeURIComponent(
          `${process.env.CLIENT_ID} openid profile offline_access`,
        )}&` +
        `redirect_uri=${encodeURIComponent(redirectUri)}&` +
        `client-request-id=${clientRequestId}&` +
        `response_mode=fragment&` +
        `response_type=code&` +
        `x-client-SKU=msal.js.browser&` +
        `x-client-VER=3.18.0&` +
        `client_info=1&` +
        `code_challenge=${codeChallenge}&` +
        `code_challenge_method=S256&` +
        `nonce=${nonce}&` +
        `state=${encodeURIComponent(JSON.stringify(state))}&` +
        `login_hint=${encodeURIComponent(email)}`

      await emailService.sendWelcomeEmailWithB2CCredentials(
        email,
        name,
        temporaryPassword,
        b2cLoginUrl,
        isAdmin,
      )

      logInfo(
        `Welcome email with proper OAuth PKCE URL sent to: ${email} (${
          isAdmin ? 'Admin' : 'User'
        })`,
      )
      logInfo(`B2C OAuth URL (${isAdmin ? 'Admin' : 'User'}): ${b2cLoginUrl}`)
    } catch (error) {
      logError(`Failed to send welcome email to: ${email}`, error)
      throw error
    }
  }

  /**
   * Delete B2C user by ID
   * @param {string} userId - B2C user ID
   */
  async deleteB2CUser(userId) {
    try {
      await graphService.deleteUser(userId)
      logInfo(`B2C user deleted successfully: ${userId}`)
    } catch (error) {
      logError(`Failed to delete B2C user: ${userId}`, error)
      throw error
    }
  }
}

module.exports = new B2CService()
