const Razorpay = require('razorpay')
const crypto = require('crypto')
const cosmosDbContext = require('../cosmosDbContext/comosdb-context')
const logging = require('../common/logging')
const { v4: uuidv4 } = require('uuid')

const paymentsContainer = 'Payments'

class PaymentService {
  constructor() {
    this.razorpay = new Razorpay({
      key_id: process.env.RAZORPAY_KEY_ID,
      key_secret: process.env.RAZORPAY_KEY_SECRET,
    })
  }

  /**
   * Create a Razorpay order
   * @param {Object} orderData - Order details
   * @param {number} orderData.amount - Amount in smallest currency unit (paise for INR)
   * @param {string} orderData.currency - Currency code (default: INR)
   * @param {string} orderData.receipt - Receipt ID for tracking
   * @param {Object} orderData.notes - Additional notes/metadata
   * @returns {Object} Razorpay order response
   */
  async createOrder(orderData) {
    try {
      const { amount, currency = 'INR', receipt, notes = {} } = orderData

      if (!amount || !receipt) {
        throw new Error('Amount and receipt are required')
      }

      const options = {
        amount: Math.round(amount * 100), // Convert to paise
        currency,
        receipt,
        notes,
      }

      logging.logInfo(`Creating Razorpay order: ${JSON.stringify(options)}`)
      const order = await this.razorpay.orders.create(options)

      // Store order in database
      const paymentRecord = {
        id: uuidv4(),
        razorpayOrderId: order.id,
        amount: amount,
        currency: currency,
        receipt: receipt,
        status: 'created',
        notes: notes,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      }

      await cosmosDbContext.createItem(paymentRecord, paymentsContainer)
      logging.logInfo(`Payment record created: ${paymentRecord.id}`)

      return {
        success: true,
        order: order,
        paymentId: paymentRecord.id,
      }
    } catch (error) {
      logging.logError('Error creating Razorpay order:', error)
      throw error
    }
  }

  /**
   * Verify payment signature
   * @param {Object} paymentData - Payment verification data
   * @param {string} paymentData.razorpay_order_id - Razorpay order ID
   * @param {string} paymentData.razorpay_payment_id - Razorpay payment ID
   * @param {string} paymentData.razorpay_signature - Razorpay signature
   * @returns {Object} Verification result
   */
  async verifyPayment(paymentData) {
    try {
      const { razorpay_order_id, razorpay_payment_id, razorpay_signature } = paymentData

      if (!razorpay_order_id || !razorpay_payment_id || !razorpay_signature) {
        throw new Error('Missing required payment verification data')
      }

      // Generate expected signature
      const body = razorpay_order_id + '|' + razorpay_payment_id
      const expectedSignature = crypto
        .createHmac('sha256', process.env.RAZORPAY_KEY_SECRET)
        .update(body.toString())
        .digest('hex')

      const isSignatureValid = expectedSignature === razorpay_signature

      if (isSignatureValid) {
        // Update payment record in database
        await this.updatePaymentStatus(razorpay_order_id, {
          status: 'completed',
          razorpayPaymentId: razorpay_payment_id,
          razorpaySignature: razorpay_signature,
          verifiedAt: new Date().toISOString(),
        })

        logging.logInfo(`Payment verified successfully: ${razorpay_payment_id}`)
        return {
          success: true,
          verified: true,
          message: 'Payment verified successfully',
        }
      } else {
        // Update payment record as failed
        await this.updatePaymentStatus(razorpay_order_id, {
          status: 'failed',
          failureReason: 'Invalid signature',
        })

        logging.logError(`Payment verification failed: Invalid signature for ${razorpay_payment_id}`)
        return {
          success: false,
          verified: false,
          message: 'Payment verification failed',
        }
      }
    } catch (error) {
      logging.logError('Error verifying payment:', error)
      throw error
    }
  }

  /**
   * Handle Razorpay webhook
   * @param {Object} webhookData - Webhook payload
   * @param {string} signature - Webhook signature
   * @returns {Object} Webhook processing result
   */
  async handleWebhook(webhookData, signature) {
    try {
      // Verify webhook signature
      const expectedSignature = crypto
        .createHmac('sha256', process.env.RAZORPAY_KEY_SECRET)
        .update(JSON.stringify(webhookData))
        .digest('hex')

      if (expectedSignature !== signature) {
        logging.logError('Invalid webhook signature')
        return { success: false, message: 'Invalid signature' }
      }

      const { event, payload } = webhookData

      logging.logInfo(`Processing webhook event: ${event}`)

      switch (event) {
        case 'payment.captured':
          await this.handlePaymentCaptured(payload.payment.entity)
          break
        case 'payment.failed':
          await this.handlePaymentFailed(payload.payment.entity)
          break
        case 'order.paid':
          await this.handleOrderPaid(payload.order.entity)
          break
        default:
          logging.logInfo(`Unhandled webhook event: ${event}`)
      }

      return { success: true, message: 'Webhook processed successfully' }
    } catch (error) {
      logging.logError('Error processing webhook:', error)
      throw error
    }
  }

  /**
   * Update payment status in database
   * @param {string} razorpayOrderId - Razorpay order ID
   * @param {Object} updateData - Data to update
   */
  async updatePaymentStatus(razorpayOrderId, updateData) {
    try {
      const query = `SELECT * FROM c WHERE c.razorpayOrderId = "${razorpayOrderId}"`
      const result = await cosmosDbContext.queryItems(query, paymentsContainer)

      if (result.length > 0) {
        const payment = result[0]
        const updatedPayment = {
          ...payment,
          ...updateData,
          updatedAt: new Date().toISOString(),
        }

        await cosmosDbContext.replaceItem(updatedPayment, payment.id, paymentsContainer)
        logging.logInfo(`Payment status updated: ${payment.id}`)
      }
    } catch (error) {
      logging.logError('Error updating payment status:', error)
      throw error
    }
  }

  /**
   * Handle payment captured webhook
   * @param {Object} payment - Payment entity from webhook
   */
  async handlePaymentCaptured(payment) {
    try {
      await this.updatePaymentStatus(payment.order_id, {
        status: 'captured',
        razorpayPaymentId: payment.id,
        capturedAt: new Date().toISOString(),
        amount: payment.amount / 100, // Convert from paise to rupees
      })
      logging.logInfo(`Payment captured: ${payment.id}`)
    } catch (error) {
      logging.logError('Error handling payment captured:', error)
    }
  }

  /**
   * Handle payment failed webhook
   * @param {Object} payment - Payment entity from webhook
   */
  async handlePaymentFailed(payment) {
    try {
      await this.updatePaymentStatus(payment.order_id, {
        status: 'failed',
        razorpayPaymentId: payment.id,
        failureReason: payment.error_description || 'Payment failed',
        failedAt: new Date().toISOString(),
      })
      logging.logInfo(`Payment failed: ${payment.id}`)
    } catch (error) {
      logging.logError('Error handling payment failed:', error)
    }
  }

  /**
   * Handle order paid webhook
   * @param {Object} order - Order entity from webhook
   */
  async handleOrderPaid(order) {
    try {
      await this.updatePaymentStatus(order.id, {
        status: 'paid',
        paidAt: new Date().toISOString(),
      })
      logging.logInfo(`Order paid: ${order.id}`)
    } catch (error) {
      logging.logError('Error handling order paid:', error)
    }
  }

  /**
   * Get payment by order ID
   * @param {string} razorpayOrderId - Razorpay order ID
   * @returns {Object} Payment record
   */
  async getPaymentByOrderId(razorpayOrderId) {
    try {
      const query = `SELECT * FROM c WHERE c.razorpayOrderId = "${razorpayOrderId}"`
      const result = await cosmosDbContext.queryItems(query, paymentsContainer)
      return result.length > 0 ? result[0] : null
    } catch (error) {
      logging.logError('Error fetching payment by order ID:', error)
      throw error
    }
  }

  /**
   * Get payment by payment ID
   * @param {string} paymentId - Internal payment ID
   * @returns {Object} Payment record
   */
  async getPaymentById(paymentId) {
    try {
      const result = await cosmosDbContext.readItem(paymentId, paymentId, paymentsContainer)
      return result
    } catch (error) {
      logging.logError('Error fetching payment by ID:', error)
      throw error
    }
  }

  /**
   * Get payments by organization
   * @param {string} organizationId - Organization ID
   * @param {number} pageSize - Page size for pagination
   * @param {string} continuationToken - Continuation token for pagination
   * @returns {Object} Paginated payments
   */
  async getPaymentsByOrganization(organizationId, pageSize = 20, continuationToken = '') {
    try {
      const query = `SELECT * FROM c WHERE c.notes.organizationId = "${organizationId}" ORDER BY c.createdAt DESC`
      const result = await cosmosDbContext.getAllItemQuery(paymentsContainer, query, pageSize, continuationToken)
      return result
    } catch (error) {
      logging.logError('Error fetching payments by organization:', error)
      throw error
    }
  }
}

module.exports = new PaymentService()
