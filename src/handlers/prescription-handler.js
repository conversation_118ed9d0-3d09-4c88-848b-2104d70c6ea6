const prescriptionService = require('../services/prescription-service')
const { HttpStatusCode } = require('axios')
const { jsonResponse } = require('../common/helper')
const productFormMapper = require('../utils/product-form-mapper')

class PrescriptionHandler {
  async getPrescriptions(req) {
    try {
      const patientId = req.query.get('patientId')
      const dateFilter = req.query.get('dateFilter') || null
      const customStartDate = req.query.get('customStartDate') || null
      const customEndDate = req.query.get('customEndDate') || null
      const searchText = req.query.get('searchText') || null

      if (!patientId) {
        return jsonResponse(
          'Missing required parameter: patientId',
          HttpStatusCode.BadRequest,
        )
      }

      const customDateRange =
        dateFilter?.trim().toLowerCase() === 'custom' &&
        customStartDate &&
        customEndDate
          ? { start: customStartDate, end: customEndDate }
          : null

      const prescriptions = await prescriptionService.getPrescriptionsByPatient(
        patientId,
        dateFilter,
        customDateRange,
        searchText,
      )

      // Transform prescriptions to use short forms
      const transformedPrescriptions =
        productFormMapper.transformPrescriptions(prescriptions)

      return jsonResponse(transformedPrescriptions)
    } catch (err) {
      return jsonResponse(
        'Error fetching prescriptions',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  async getPrescriptionDetails(req) {
    try {
      const prescriptionId = req.query.get('prescriptionId')
      if (!prescriptionId) {
        return jsonResponse(
          'Missing patient ID or prescription ID',
          HttpStatusCode.BadRequest,
        )
      }

      const prescription = await prescriptionService.getPrescriptionById(
        prescriptionId,
      )

      // Transform prescription to use short forms
      const transformedPrescription =
        productFormMapper.transformPrescriptions(prescription)

      return jsonResponse(transformedPrescription)
    } catch (err) {
      return jsonResponse(
        'Error fetching prescription details',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  async createOrUpdatePrescription(req) {
    try {
      const body = await req.json()
      const { patientId, medicines, doctor, doctorEmail } = body 

      const prescriptionId = req.query.get('prescriptionId')
      if (
        !patientId ||
        !doctor ||
        !Array.isArray(medicines) ||
        medicines.length === 0
      ) {
        return jsonResponse(
          'Invalid data for prescription',
          HttpStatusCode.BadRequest,
        )
      }

      const result = await prescriptionService.createOrUpdatePrescriptions(
        patientId,
        doctor,
        medicines,
        prescriptionId,
        doctorEmail, 
      )

      return jsonResponse(result, HttpStatusCode.Created)
    } catch (err) {
      console.error('Prescription error:', err)
      return jsonResponse(
        'Error creating or updating prescription',
        HttpStatusCode.InternalServerError,
      )
    }
  }
  async searchPrescriptions(req) {
    try {
      const body = await req.json()
      const {
        searchText = '',
        pageSize = 10,
        continuationToken = '',
        patientId = null,
      } = body

      if (!searchText.trim()) {
        return jsonResponse('Missing search text', HttpStatusCode.BadRequest)
      }

      const result = await prescriptionService.searchPrescriptions(
        searchText,
        pageSize,
        continuationToken,
        patientId,
      )

      // Transform search results to use short forms
      if (result && result.items) {
        result.items = productFormMapper.transformPrescriptions(result.items)
      }

      return jsonResponse(result)
    } catch (err) {
      console.error('Search prescriptions error:', err)
      return jsonResponse(
        'Error searching prescriptions',
        HttpStatusCode.InternalServerError,
      )
    }
  }
}
module.exports = new PrescriptionHandler()
