const labTestService = require('../services/patient-lab-test-service')
const { HttpStatusCode } = require('axios')
const { jsonResponse } = require('../common/helper')

class LabTestHandler {
  async getLabTests(req) {
    try {
      const patientId = req.query.get('patientId')
      const dateFilter = req.query.get('dateFilter') || null
      const sortField = req.query.get('sortField') || null
      const sortOrder = req.query.get('sortOrder') || 'asc'
      const customStartDate = req.query.get('customStartDate') || null
      const customEndDate = req.query.get('customEndDate') || null
      const searchText = req.query.get('searchText') || null
      const department = req.query.get('department') || 'ALL'

      if (!patientId) {
        return jsonResponse(
          'Missing required parameter: patientId',
          HttpStatusCode.BadRequest,
        )
      }

      const customDateRange =
        dateFilter === 'custom' && customStartDate && customEndDate
          ? { start: customStartDate, end: customEndDate }
          : null

      const data = await labTestService.getLabTestsByPatient(
        patientId,
        dateFilter,
        sortField,
        sortOrder,
        customDateRange,
        searchText,
        department,
      )

      return jsonResponse(data)
    } catch (err) {
      console.error('Error fetching lab tests:', err)
      return jsonResponse(
        'Failed to fetch lab tests',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  async createLabTest(req) {
    try {
      const body = await req.json()
      const { patientId, labTests } = body
      if (!patientId || !Array.isArray(labTests) || labTests.length === 0) {
        return jsonResponse(
          'Invalid data for lab tests',
          HttpStatusCode.BadRequest,
        )
      }

      const result = await labTestService.createLabTestsForPatient(
        patientId,
        labTests,
      )
      return jsonResponse(result, HttpStatusCode.Created)
    } catch (err) {
      console.error('Error creating lab tests:', err)
      return jsonResponse(
        'Failed to create lab tests',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  async updateLabTest(req) {
    try {
      const data = await req.json()
      const result = await labTestService.updateLabTest(data)
      return jsonResponse(result)
    } catch (err) {
      return jsonResponse(
        'Failed to update lab test',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  async deleteLabTest(req) {
    try {
      const id = req.query.get('id')
      const result = await labTestService.deleteLabTest(id)
      return jsonResponse(result)
    } catch (err) {
      return jsonResponse(
        'Failed to delete lab test',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  async getLabTestDetails(req) {
    try {
      const labTestId = req.query.get('labTestId')
      if (!labTestId) {
        return jsonResponse(
          'Missing patient ID or labtest ID',
          HttpStatusCode.BadRequest,
        )
      }

      const labTest = await labTestService.getLabTestById(labTestId)
      return jsonResponse(labTest)
    } catch (err) {
      return jsonResponse(
        'Error fetching labtest details',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  async searchPatientLabTest(req) {
    try {
      const body = await req.json()
      const {
        searchText = '',
        pageSize = 10,
        continuationToken = '',
        patientId = null,
      } = body

      if (!searchText.trim()) {
        return jsonResponse('Missing search text', HttpStatusCode.BadRequest)
      }

      const result = await labTestService.searchPatientLabTest(
        searchText,
        pageSize,
        continuationToken,
        patientId,
      )

      return jsonResponse(result)
    } catch (err) {
      console.error('Search lab test error:', err)
      return jsonResponse(
        'Error searching lab test',
        HttpStatusCode.InternalServerError,
      )
    }
  }
}

module.exports = new LabTestHandler()
