const openAIService = require('../services/openai-service');
const logging = require('../common/logging');

class SummaryHandler {

    async identifySpeaker(transctipt) {
        try {
            var classification = "you are an AI assistant help doctor to separate the chat transcript to know which one is doctor which one is patient, following the format:\n\n[{\"speaker\": \"doctor\", \"message\": \"\"},{\"speaker\": \"patient\", \"message\": \"\"}]\n\ncombine into 1 inline JSON without '\\n'\n The result must be in the format above strictly, if you don't see any information to analytic then just return the json format above with the null value";
            var result = await openAIService.chatCompletion(classification, transctipt);
            return result;
        } catch (error) {
            logging.logError("Unable to identify Speaker", error);
            return null;
        }
    }

    async sumnmaryConversation(conversation) {
        try {
            const summaryInfo = process.env.SummaryInfo
            var classification = "In a clinic at a hospital in the afternoon, a doctor is conducting a routine examination for a patient. The doctor inquires about the patient's personal medical history, family medical history, and daily lifestyle habits.\n\nOutput Request: From the conversation below, please analyze and return detailed information in JSON format without '```json\\n' for the following items: "+ summaryInfo + ". The output must strictly follow the items mentioned above, and must include all the items listed. The value for each item should be in HTML format. If there is no information available, return a plain value for those items. Include bullet points where necessary.\n\nAdditionally:\nAny medical conditions, diagnoses, or terms must be identified and classified using their respective codes from the International Classification of Diseases (ICD). You do not need to show the note that where you got the information from ICD data, for example: 'Hypertension (ICD-10: I10)' then you do not show '(ICD-10: I10)'.\nThe result response must be translated to English only.";
            var result = await openAIService.chatCompletion(classification, conversation);
            return result;
        } catch (error) {
            logging.logError(error);
            return null;
        }
    }

}

module.exports = new SummaryHandler();