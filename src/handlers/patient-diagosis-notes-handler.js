const logging = require('../common/logging');
const patientService = require('../services/patient-service');

class PatientDiagnosisNotesHandler {

    async getPatientDiagnosisNotes(patientId) {
        try {
            return await patientService.getPatientDiagnosisNotes(patientId);
        } catch (error) {
            logging.logError(`Unable to get patient diagnosis notes for patient ${patientId}`, error);
            return null;
        }
    }

    async createPatientDiagnosisNotes(patientId, diagnosisNotes, create_by) {
        try {
            diagnosisNotes.create_by = create_by;
            diagnosisNotes.update_by = create_by;
            return await patientService.createPatientDiagnosisNotes(patientId, diagnosisNotes);
        } catch (error) {
            logging.logError(`Unable to create patient diagnosis notes for patient ${patientId}`, error);
            return null;
        }
    }

    async patchPatientDiagnosisNotes(id, diagnosisNotes, update_by) {
        try {
            diagnosisNotes.update_by = update_by;
            return await patientService.patchPatientDiagnosisNotes(id, diagnosisNotes);
        } catch (error) {
            logging.logError(`Unable to patch patient diagnosis notes for patient ${patientId}`, error);
            return null;
        }
    }

    async getPatientDiagnosisNotesByQuery(query) {
        try {
            var data = await patientService.getPatientDiagnosisNotesByQuery(query);
            return data;
        } catch (error) {
            logging.logError(`Unable to get patient diagnosis notes by query ${query}`, error);
            return null;
        }
    }

}

module.exports = new PatientDiagnosisNotesHandler();