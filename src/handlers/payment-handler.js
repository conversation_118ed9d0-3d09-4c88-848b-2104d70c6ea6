const paymentService = require('../services/payment-service')
const { jsonResponse } = require('../common/helper')
const { HttpStatusCode } = require('axios')
const logging = require('../common/logging')
const { v4: uuidv4 } = require('uuid')

class PaymentHandler {
  /**
   * Create a new payment order
   * @param {Object} req - HTTP request object
   * @returns {Object} HTTP response
   */
  async createOrder(req) {
    try {
      const body = await req.json()
      const {
        amount,
        currency = 'INR',
        patientId,
        organizationId,
        appointmentId,
        description,
        metadata = {},
      } = body

      // Validate required fields
      if (!amount || !patientId || !organizationId) {
        return jsonResponse(
          'Missing required fields: amount, patientId, organizationId',
          HttpStatusCode.BadRequest,
        )
      }

      if (amount <= 0) {
        return jsonResponse('Amount must be greater than 0', HttpStatusCode.BadRequest)
      }

      // Generate unique receipt ID
      const receipt = `rcpt_${uuidv4().substring(0, 8)}_${Date.now()}`

      // Prepare order data
      const orderData = {
        amount: parseFloat(amount),
        currency,
        receipt,
        notes: {
          patientId,
          organizationId,
          appointmentId: appointmentId || null,
          description: description || 'EMR Payment',
          ...metadata,
        },
      }

      logging.logInfo(`Creating payment order for patient: ${patientId}, organization: ${organizationId}`)

      const result = await paymentService.createOrder(orderData)

      return jsonResponse({
        success: true,
        message: 'Payment order created successfully',
        data: {
          orderId: result.order.id,
          paymentId: result.paymentId,
          amount: result.order.amount,
          currency: result.order.currency,
          receipt: result.order.receipt,
          status: result.order.status,
          keyId: process.env.RAZORPAY_KEY_ID, // Frontend needs this for payment
        },
      })
    } catch (error) {
      logging.logError('Error creating payment order:', error)
      return jsonResponse(
        'Failed to create payment order',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  /**
   * Verify payment after successful payment
   * @param {Object} req - HTTP request object
   * @returns {Object} HTTP response
   */
  async verifyPayment(req) {
    try {
      const body = await req.json()
      const { razorpay_order_id, razorpay_payment_id, razorpay_signature } = body

      // Validate required fields
      if (!razorpay_order_id || !razorpay_payment_id || !razorpay_signature) {
        return jsonResponse(
          'Missing required fields: razorpay_order_id, razorpay_payment_id, razorpay_signature',
          HttpStatusCode.BadRequest,
        )
      }

      logging.logInfo(`Verifying payment: ${razorpay_payment_id}`)

      const result = await paymentService.verifyPayment({
        razorpay_order_id,
        razorpay_payment_id,
        razorpay_signature,
      })

      if (result.verified) {
        return jsonResponse({
          success: true,
          verified: true,
          message: 'Payment verified successfully',
          paymentId: razorpay_payment_id,
        })
      } else {
        return jsonResponse(
          {
            success: false,
            verified: false,
            message: 'Payment verification failed',
          },
          HttpStatusCode.BadRequest,
        )
      }
    } catch (error) {
      logging.logError('Error verifying payment:', error)
      return jsonResponse(
        'Failed to verify payment',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  /**
   * Handle Razorpay webhooks
   * @param {Object} req - HTTP request object
   * @returns {Object} HTTP response
   */
  async handleWebhook(req) {
    try {
      const signature = req.headers.get('x-razorpay-signature')
      const body = await req.json()

      if (!signature) {
        return jsonResponse('Missing webhook signature', HttpStatusCode.BadRequest)
      }

      logging.logInfo(`Processing webhook: ${body.event}`)

      const result = await paymentService.handleWebhook(body, signature)

      if (result.success) {
        return jsonResponse({
          success: true,
          message: 'Webhook processed successfully',
        })
      } else {
        return jsonResponse(
          {
            success: false,
            message: result.message,
          },
          HttpStatusCode.BadRequest,
        )
      }
    } catch (error) {
      logging.logError('Error processing webhook:', error)
      return jsonResponse(
        'Failed to process webhook',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  /**
   * Get payment details by order ID
   * @param {Object} req - HTTP request object
   * @returns {Object} HTTP response
   */
  async getPaymentByOrderId(req) {
    try {
      const orderId = req.query.get('orderId')

      if (!orderId) {
        return jsonResponse('Missing orderId parameter', HttpStatusCode.BadRequest)
      }

      logging.logInfo(`Fetching payment details for order: ${orderId}`)

      const payment = await paymentService.getPaymentByOrderId(orderId)

      if (!payment) {
        return jsonResponse('Payment not found', HttpStatusCode.NotFound)
      }

      return jsonResponse({
        success: true,
        data: payment,
      })
    } catch (error) {
      logging.logError('Error fetching payment by order ID:', error)
      return jsonResponse(
        'Failed to fetch payment details',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  /**
   * Get payment details by payment ID
   * @param {Object} req - HTTP request object
   * @returns {Object} HTTP response
   */
  async getPaymentById(req) {
    try {
      const paymentId = req.query.get('paymentId')

      if (!paymentId) {
        return jsonResponse('Missing paymentId parameter', HttpStatusCode.BadRequest)
      }

      logging.logInfo(`Fetching payment details for payment: ${paymentId}`)

      const payment = await paymentService.getPaymentById(paymentId)

      if (!payment) {
        return jsonResponse('Payment not found', HttpStatusCode.NotFound)
      }

      return jsonResponse({
        success: true,
        data: payment,
      })
    } catch (error) {
      logging.logError('Error fetching payment by ID:', error)
      return jsonResponse(
        'Failed to fetch payment details',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  /**
   * Get payments for an organization
   * @param {Object} req - HTTP request object
   * @returns {Object} HTTP response
   */
  async getOrganizationPayments(req) {
    try {
      const organizationId = req.query.get('organizationId')
      const pageSize = parseInt(req.query.get('pageSize')) || 20
      const continuationToken = req.query.get('continuationToken') || ''

      if (!organizationId) {
        return jsonResponse('Missing organizationId parameter', HttpStatusCode.BadRequest)
      }

      logging.logInfo(`Fetching payments for organization: ${organizationId}`)

      const result = await paymentService.getPaymentsByOrganization(
        organizationId,
        pageSize,
        continuationToken,
      )

      return jsonResponse({
        success: true,
        data: result.items || [],
        pagination: {
          pageSize,
          continuationToken: result.continuationToken || null,
          hasMore: !!result.continuationToken,
        },
      })
    } catch (error) {
      logging.logError('Error fetching organization payments:', error)
      return jsonResponse(
        'Failed to fetch organization payments',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  /**
   * Get payment statistics for an organization
   * @param {Object} req - HTTP request object
   * @returns {Object} HTTP response
   */
  async getPaymentStats(req) {
    try {
      const organizationId = req.query.get('organizationId')

      if (!organizationId) {
        return jsonResponse('Missing organizationId parameter', HttpStatusCode.BadRequest)
      }

      logging.logInfo(`Fetching payment statistics for organization: ${organizationId}`)

      // This is a basic implementation - you can enhance it with more detailed statistics
      const payments = await paymentService.getPaymentsByOrganization(organizationId, 1000)
      const paymentList = payments.items || []

      const stats = {
        totalPayments: paymentList.length,
        totalAmount: paymentList.reduce((sum, payment) => sum + (payment.amount || 0), 0),
        completedPayments: paymentList.filter(p => p.status === 'completed').length,
        failedPayments: paymentList.filter(p => p.status === 'failed').length,
        pendingPayments: paymentList.filter(p => p.status === 'created').length,
      }

      return jsonResponse({
        success: true,
        data: stats,
      })
    } catch (error) {
      logging.logError('Error fetching payment statistics:', error)
      return jsonResponse(
        'Failed to fetch payment statistics',
        HttpStatusCode.InternalServerError,
      )
    }
  }
}

module.exports = new PaymentHandler()
