module.exports = {
  getAllMedicinesQuery: () => {
    return `SELECT * FROM c`
  },
  getOrganizationMedicinesQuery: (organizationId) => {
    return `SELECT * FROM c WHERE c.organizationId = "${organizationId}"`
  },
  getMedicinesByProductIdsQuery: (productIds) => {
    return `SELECT * FROM c WHERE c.productId IN (${productIds
      .map((id) => `"${id}"`)
      .join(', ')})`
  },
  getOrganizationMedicineIdsWithPricingQuery: (organizationId) => {
    return `SELECT c.medicineId, c.price FROM c WHERE c.organizationId = "${organizationId}" AND c.isActive = true`
  },
  searchMedicinesByIdsQuery: (medicineIds, searchText) => {
    const idsString = medicineIds.map((id) => `"${id}"`).join(', ')
    return `SELECT * FROM c WHERE c.id IN (${idsString}) AND (CONTAINS(LOWER(c.saltComposition), LOWER('${searchText}')) OR CONTAINS(LOWER(c.productName), LOWER('${searchText}')))`
  },
}
