const cosmosDbContext = require('../cosmosDbContext/comosdb-context')
const logging = require('../common/logging')

const medicineContainer = 'medicines'
const organizationMedicinesContainer = 'OrganizationMedicines'

class MedicineRepository {
  async fetchMedicinesForOrganization(
    organizationId,
    searchText,
    pageSize,
    page,
    continuationToken = null,
  ) {
    try {
      let actualPage = page
      if (continuationToken && continuationToken.startsWith('page_')) {
        actualPage = parseInt(continuationToken.replace('page_', ''))
      }

      const orgMedicinesMap = await this.getOrganizationMedicinesMap(
        organizationId,
      )

      const countQuery = this.buildMedicineCountQuery(searchText)
      const countResult = await cosmosDbContext.queryItems(
        countQuery,
        medicineContainer,
      )
      const totalCount = countResult[0] || 0
      const totalPages = Math.ceil(totalCount / pageSize)

      const medicineQuery = this.buildOptimizedMedicineQuery(
        searchText,
        pageSize,
        actualPage,
      )

      const allResults = await cosmosDbContext.queryItems(
        medicineQuery,
        medicineContainer,
      )

      const hasMoreResults = allResults.length > pageSize
      const actualResults = hasMoreResults
        ? allResults.slice(0, pageSize)
        : allResults

      const nextPageToken = hasMoreResults ? `page_${actualPage + 1}` : null

      const transformedItems = actualResults.map((medicine, index) => {
        const orgData = orgMedicinesMap.get(medicine.id) || {
          isActive: false, // Default to false if medicine not in organization container
          price: 0,
        }

        return {
          ...medicine,
          isActive: orgData.isActive,
          price: orgData.price,
        }
      })

      const response = {
        items: transformedItems,
        continuationToken: nextPageToken,
        hasMoreResults: hasMoreResults,
        currentPage: actualPage,
        pageSize: pageSize,
        totalFetched: actualResults.length,
        totalCount: totalCount,
        totalPages: totalPages,
      }

      return response
    } catch (error) {
      if (error.name === 'RestError' && error.code === 'PARSE_ERROR') {
        logging.logError(
          `Failed to fetch medicines for organization due to parse error: ${error.message}`,
          error,
        )
      } else {
        logging.logError(
          `Failed to fetch medicines for organization: ${error.message}`,
          error,
        )
      }
      throw new Error('Failed to fetch medicines for organization')
    }
  }

  buildMedicineQueryConditions(searchText) {
    const conditions = []

    if (searchText && searchText.trim() !== '') {
      const searchCondition = `(
        CONTAINS(UPPER(c.productName), UPPER("${searchText}")) OR
        CONTAINS(UPPER(c.saltComposition), UPPER("${searchText}"))
      )`
      conditions.push(searchCondition)
    }

    return conditions
  }

  buildMedicineCountQuery(searchText) {
    let query = `SELECT VALUE COUNT(1) FROM c`
    const conditions = this.buildMedicineQueryConditions(searchText)

    if (conditions.length > 0) {
      query += ` WHERE ${conditions.join(' AND ')}`
    }

    return query
  }

  buildOptimizedMedicineQuery(searchText, pageSize, page) {
    let query = `SELECT * FROM c`
    const conditions = this.buildMedicineQueryConditions(searchText)

    if (conditions.length > 0) {
      query += ` WHERE ${conditions.join(' AND ')}`
    }

    query += ` ORDER BY c.id`

    const offset = (page - 1) * pageSize
    query += ` OFFSET ${offset} LIMIT ${pageSize + 1}` // +1 to check if there are more results

    return query
  }

  async getOrganizationMedicinesMap(organizationId) {
    try {
      const query = `SELECT c.medicineId, c.isActive, c.price FROM c WHERE c.organizationId = "${organizationId}"`

      const orgMedicines = await cosmosDbContext.queryItems(
        query,
        organizationMedicinesContainer,
      )
      const orgMedicinesMap = new Map()
      orgMedicines.forEach((medicine) => {
        orgMedicinesMap.set(medicine.medicineId, {
          isActive: medicine.isActive !== undefined ? medicine.isActive : false,
          price: medicine.price || 0,
        })
      })

      return orgMedicinesMap
    } catch (error) {
      logging.logError('Error fetching organization medicines map:', error)
      return new Map()
    }
  }

  async updateOrganizationMedicines(
    organizationId,
    medicines = [],
    selectAll = false,
  ) {
    try {
      const updatedMedicines = []

      let medicinesToUpdate = []

      if (selectAll) {
        logging.logInfo('Fetching all medicines for selectAll functionality')
        const allMedicinesQuery = `SELECT * FROM c`
        const allMedicines = await cosmosDbContext.queryItems(
          allMedicinesQuery,
          medicineContainer,
        )
        logging.logInfo(
          `Fetched ${allMedicines.length} medicines for selectAll`,
        )

        medicinesToUpdate = allMedicines.map((medicine) => ({
          medicineId: medicine.id,
          isActive: true,
        }))
      } else if (Array.isArray(medicines) && medicines.length > 0) {
        medicinesToUpdate = medicines
      } else {
        throw new Error(
          'Missing required fields: organizationId or medicines when selectAll is false',
        )
      }

      const existingMedicinesQuery = `SELECT * FROM c WHERE c.organizationId = "${organizationId}"`
      const existingMedicines = await cosmosDbContext.queryItems(
        existingMedicinesQuery,
        organizationMedicinesContainer,
      )
      const existingMedicineIds = new Set(
        existingMedicines.map((med) => med.medicineId),
      )

      const newMedicines = []
      const updatedExistingMedicines = []

      for (const medicine of medicinesToUpdate) {
        const { medicineId, isActive, price } = medicine

        if (existingMedicineIds.has(medicineId)) {
          const existingMedicine = existingMedicines.find(
            (med) => med.medicineId === medicineId,
          )
          existingMedicine.isActive = isActive
          if (!selectAll) {
            existingMedicine.price = price // Update price only when selectAll is false
          }
          existingMedicine.updatedOn = new Date().toISOString()
          updatedExistingMedicines.push(existingMedicine)
        } else {
          const newMedicine = {
            organizationId,
            medicineId,
            isActive: true,
            price: price || 0,
            createdOn: new Date().toISOString(),
            updatedOn: new Date().toISOString(),
          }
          newMedicines.push(newMedicine)
        }
      }

      const batchSize = 200
      for (let i = 0; i < updatedExistingMedicines.length; i += batchSize) {
        const batch = updatedExistingMedicines.slice(i, i + batchSize)
        await Promise.all(
          batch.map((medicine) =>
            cosmosDbContext.updateItem(
              medicine,
              organizationMedicinesContainer,
            ),
          ),
        )
        updatedMedicines.push(...batch)
      }

      for (let i = 0; i < newMedicines.length; i += batchSize) {
        const batch = newMedicines.slice(i, i + batchSize)
        await Promise.all(
          batch.map((medicine) =>
            cosmosDbContext.createItem(
              medicine,
              organizationMedicinesContainer,
            ),
          ),
        )
        updatedMedicines.push(...batch)
      }

      logging.logInfo('Finished updateOrganizationMedicines method')
      return updatedMedicines
    } catch (error) {
      logging.logError('Error updating organization medicines', error)
      throw new Error('Failed to update organization medicines')
    }
  }

  async getOrganizationMedicineIdsWithPricing(organizationId) {
    try {
      const {
        getOrganizationMedicineIdsWithPricingQuery,
      } = require('../queries/medicine-query')
      const query = getOrganizationMedicineIdsWithPricingQuery(organizationId)

      const result = await cosmosDbContext.queryItems(
        query,
        organizationMedicinesContainer,
      )

      return result
    } catch (error) {
      logging.logError(
        `Error fetching organization medicine IDs with pricing for org ${organizationId}:`,
        error,
      )
      return []
    }
  }

  async searchMedicinesByIds(
    medicineIds,
    searchText,
    pageSize,
    continuationToken,
  ) {
    try {
      const { searchMedicinesByIdsQuery } = require('../queries/medicine-query')
      const query = searchMedicinesByIdsQuery(medicineIds, searchText)

      const result = await cosmosDbContext.getAllItemQuery(
        medicineContainer,
        query,
        pageSize,
        continuationToken,
      )

      return result
    } catch (error) {
      logging.logError(
        `Error searching medicines by IDs with search text "${searchText}":`,
        error,
      )
      return { items: [], continuationToken: null }
    }
  }

  async removeOrganizationMedicines(
    organizationId,
    medicines,
    selectAll = false,
  ) {
    try {
      const removedMedicines = []
      let medicinesToRemove = medicines

      if (selectAll) {
        // Get all medicines for the organization
        const allOrgMedicinesQuery = `SELECT * FROM c WHERE c.organizationId = "${organizationId}"`
        const allOrgMedicines = await cosmosDbContext.queryItems(
          allOrgMedicinesQuery,
          organizationMedicinesContainer,
        )
        medicinesToRemove = allOrgMedicines.map(
          (medicine) => medicine.medicineId,
        )
      }

      if (!medicinesToRemove || medicinesToRemove.length === 0) {
        return removedMedicines
      }

      // Get existing organization medicines to remove
      const existingMedicinesQuery = `SELECT * FROM c WHERE c.organizationId = "${organizationId}" AND c.medicineId IN (${medicinesToRemove
        .map((id) => `"${id}"`)
        .join(', ')})`
      const existingMedicines = await cosmosDbContext.queryItems(
        existingMedicinesQuery,
        organizationMedicinesContainer,
      )

      // Remove medicines in batches
      const batchSize = 100
      for (let i = 0; i < existingMedicines.length; i += batchSize) {
        const batch = existingMedicines.slice(i, i + batchSize)

        await Promise.all(
          batch.map(async (medicine) => {
            await cosmosDbContext.deleteItem(
              medicine.id,
              medicine.id,
              organizationMedicinesContainer,
            )
            removedMedicines.push(medicine.medicineId)
          }),
        )

        // Add delay between batches to avoid rate limiting
        if (i + batchSize < existingMedicines.length) {
          await new Promise((resolve) => setTimeout(resolve, 200))
        }
      }

      logging.logInfo(
        `Removed ${removedMedicines.length} medicines from organization ${organizationId}`,
      )
      return removedMedicines
    } catch (error) {
      logging.logError('Error removing organization medicine details', error)
      throw new Error('Failed to remove organization medicine details')
    }
  }

  // Async version with progress tracking
  async removeOrganizationMedicinesAsync(
    organizationId,
    medicines,
    selectAll = false,
    progressCallback = null,
  ) {
    try {
      const removedMedicines = []
      let medicinesToRemove = medicines

      if (progressCallback) {
        progressCallback('Determining medicines to remove...')
      }

      if (selectAll) {
        // Get all medicines for the organization
        const allOrgMedicinesQuery = `SELECT * FROM c WHERE c.organizationId = "${organizationId}"`
        const allOrgMedicines = await cosmosDbContext.queryItems(
          allOrgMedicinesQuery,
          organizationMedicinesContainer,
        )
        medicinesToRemove = allOrgMedicines.map(
          (medicine) => medicine.medicineId,
        )
      }

      if (!medicinesToRemove || medicinesToRemove.length === 0) {
        if (progressCallback) {
          progressCallback('No medicines to remove')
        }
        return removedMedicines
      }

      if (progressCallback) {
        progressCallback(
          `Found ${medicinesToRemove.length} medicines to remove`,
        )
      }

      // Get existing organization medicines to remove
      const existingMedicinesQuery = `SELECT * FROM c WHERE c.organizationId = "${organizationId}" AND c.medicineId IN (${medicinesToRemove
        .map((id) => `"${id}"`)
        .join(', ')})`
      const existingMedicines = await cosmosDbContext.queryItems(
        existingMedicinesQuery,
        organizationMedicinesContainer,
      )

      // Remove medicines in batches with progress tracking
      const batchSize = 100
      for (let i = 0; i < existingMedicines.length; i += batchSize) {
        const batch = existingMedicines.slice(i, i + batchSize)

        if (progressCallback) {
          progressCallback(
            `Removing batch ${Math.floor(i / batchSize) + 1} of ${Math.ceil(
              existingMedicines.length / batchSize,
            )}`,
          )
        }

        await Promise.all(
          batch.map(async (medicine) => {
            await cosmosDbContext.deleteItem(
              medicine.id,
              medicine.id,
              organizationMedicinesContainer,
            )
            removedMedicines.push(medicine.medicineId)
          }),
        )

        // Add delay between batches to avoid rate limiting
        if (i + batchSize < existingMedicines.length) {
          await new Promise((resolve) => setTimeout(resolve, 200))
        }
      }

      if (progressCallback) {
        progressCallback(
          `Successfully removed ${removedMedicines.length} medicines`,
        )
      }

      logging.logInfo(
        `Removed ${removedMedicines.length} medicines from organization ${organizationId}`,
      )
      return removedMedicines
    } catch (error) {
      logging.logError('Error removing organization medicine details', error)
      if (progressCallback) {
        progressCallback(`Error: ${error.message}`)
      }
      throw new Error('Failed to remove organization medicine details')
    }
  }
}

module.exports = new MedicineRepository()
