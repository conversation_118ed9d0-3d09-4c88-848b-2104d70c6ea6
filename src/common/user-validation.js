// const msal = require('@azure/msal-node');
const logging = require('./logging')
const userService = require('../services/user-service')
const jwt = require('jsonwebtoken')
const jwksClient = require('jwks-rsa')
const axios = require('axios').default
const rolePermissionHandler = require('../handlers/role-permission-handler')
const RedisCacheHandler = require('../services/redis-service')
const https = require('https')
const { AuthMessage } = require('./constant')
const agent = new https.Agent({
  rejectUnauthorized: false,
})
const exp = require('constants')
const fs = require('fs')
const path = require('path')
const { DefaultRoles } = require('./roles')

const TENANT_NAME = process.env.TENANT_NAME
const SIGNIN_POLICY = process.env.signin_policy
const EXPIRED_TIME = 10 * 60 * 1000

const getSigningKeys = async () => {
  const jwksUri = `https://${TENANT_NAME}.b2clogin.com/${TENANT_NAME}.onmicrosoft.com/discovery/v2.0/keys?p=${SIGNIN_POLICY}`
  const response = await axios.get(jwksUri, {
    httpsAgent: agent,
  })
  return response.data.keys
}
const client = jwksClient({
  jwksUri: `https://${TENANT_NAME}.b2clogin.com/${TENANT_NAME}.onmicrosoft.com/discovery/v2.0/keys?p=${SIGNIN_POLICY}`,
})
function getKey(header, callback) {
  client.getSigningKey(header.kid, function (err, key) {
    const signingKey = key.getPublicKey()
    callback(null, signingKey)
  })
}
const verifyToken = async (token) => {
  try {
    const keys = await getSigningKeys()
    const decodedToken = jwt.decode(token, { complete: true })

    const kid = decodedToken.header.kid
    const signingKey = keys.find((key) => key.kid === kid)

    if (!signingKey) {
      throw new Error('Signing key not found')
    }

    return new Promise((resolve, reject) => {
      jwt.verify(token, getKey, { algorithms: ['RS256'] }, (err, decoded) => {
        if (err) {
          return reject(err)
        }
        resolve(decoded)
      })
    })
  } catch (error) {
    // Fallback: Verify using RSA public key
    const publicKey = fs.readFileSync(
      path.resolve(__dirname, '../keys/public.pem'),
      'utf8',
    )

    return new Promise((resolve, reject) => {
      jwt.verify(
        token,
        publicKey,
        { algorithms: ['RS256'] },
        (err, decoded) => {
          if (err) {
            return reject(err)
          }
          resolve(decoded)
        },
      )
    })
  }
}
class ValidateUser {
  /**
   * @param {*} req azure functin req
   * @param {*} context azure functin context
   * @param {*} idToken jwt token frm request header
   * @param {*} role user role need to verify
   * @returns
   */
  async doValidate(req) {
    try {
      if (process.env.environment && process.env.environment == 'local') {
        return {
          message: AuthMessage.SUCCESS,
          decode: {
            oid: 'local_debugging',
          },
        }
      }
      var url = req.url
      var apiName = new URL(url).pathname.split('/api/')[1]
      apiName = apiName.split('/').join('-')
      var method = req.method

      // Exclude token validation for specific endpoints
      if (apiName === 'auth-login' || apiName === 'user-set-password') {
        return {
          message: AuthMessage.SUCCESS,
          decode: null,
        }
      }
      if (apiName == 'usersignup') {
        return {
          message: AuthMessage.SUCCESS,
          decode: {
            oid: 'Azure B2C',
          },
        }
      }
      const token = req.headers.get('authorization')
      var idToken = token?.split(' ')[1] || null
      if (!idToken) {
        return {
          message: AuthMessage.MISSING_TOKEN,
          decode: null,
        }
      }
      var decode = await verifyToken(idToken)
      if (decode) {
        var userid = decode.oid
        //--------------- Commented Code
        // let lastLogin = await RedisCacheHandler.get(userid)
        // if (!lastLogin) {
        //   await RedisCacheHandler.set(userid, new Date().toISOString())
        // } else {
        //   const now = new Date()
        //   const last = new Date(lastLogin)
        //   const diff = now - last

        //   if (diff >= EXPIRED_TIME) {
        //     await RedisCacheHandler.delete(userid)
        //     return {
        //       message: AuthMessage.SESSION_EXPIRED,
        //       decode: null,
        //     }
        //   }

        //   await RedisCacheHandler.set(userid, new Date().toISOString())
        //   lastLogin = await RedisCacheHandler.get(userid)
        // }
        //---------------
        var isValidUser = await checkUserRole(apiName, method, userid)
        if (!isValidUser) {
          return {
            message: AuthMessage.NO_PERMISSING,
            decode: null,
          }
        }
        return {
          message: AuthMessage.SUCCESS,
          decode: decode,
        }
      }
      return {
        message: AuthMessage.COMMON_AUTH_FAILED,
        decode: null,
      }
    } catch (error) {
      logging.logError('', error)
      return {
        message: error.message,
        decode: null,
      }
    }
  }
}

async function checkUserRole(apiName, method, userid) {
  try {
    let user = await userService.getUserByB2CUserId(userid)
    if (!user) {
      user = await userService.getUserById(userid)
    }

    if (!user) {
      logging.logInfo(`User not found: ${userid}`)
      return false
    }

    const userRole = user?.userRole || ''
    const roleId = user?.roleId || ''

    logging.logInfo(
      `Checking user permissions for ${userid}: userRole=${userRole}, roleId=${roleId}, apiName=${apiName}, method=${method}`,
    )

    if (userRole === DefaultRoles.SUPER_ADMIN) {
      logging.logInfo(
        `User ${userid} granted unrestricted access as Super Admin`,
      )
      return true
    }

    if (
      roleId &
      (userRole !== DefaultRoles.ORGANIZATION_ADMIN &&
        userRole !== DefaultRoles.DOCTOR &&
        userRole !== DefaultRoles.NURSE &&
        userRole !== DefaultRoles.RECEPTIONIST &&
        userRole !== DefaultRoles.ORGANIZATION_SUPER_ADMIN)
    ) {
      const organizationId = user?.organizationId || ''
      let rolePermissions = null

      if (organizationId) {
        rolePermissions =
          await rolePermissionHandler.getAPIListbyRoleIdAndOrganization(
            roleId,
            organizationId,
          )
      }
      if (!rolePermissions) {
        rolePermissions = await rolePermissionHandler.getAPIListbyRoleId(roleId)
      }

      if (!rolePermissions || !rolePermissions.APIs) {
        logging.logInfo(
          `No role permissions found for user ${userid} with roleId ${roleId}`,
        )
        return false
      }
      const apiPermission = rolePermissions.APIs.find(
        (api) => api.api === apiName,
      )

      if (!apiPermission) {
        logging.logInfo(
          `No API permission found for ${apiName} for user ${userid} with roleId ${roleId}`,
        )
        return false
      }

      const hasMethodPermission = apiPermission.methods.includes(
        method.toUpperCase(),
      )
      logging.logInfo(
        `Method permission check for ${method} on ${apiName}: ${hasMethodPermission} (roleId: ${roleId}, organizationId: ${organizationId})`,
      )
      return hasMethodPermission
    }
    logging.logInfo(
      `No roleId found for user ${userid}, falling back to role-based checking`,
    )

    if (
      userRole === DefaultRoles.ORGANIZATION_ADMIN ||
      userRole === DefaultRoles.DOCTOR ||
      userRole === DefaultRoles.NURSE ||
      userRole === DefaultRoles.RECEPTIONIST ||
      userRole === DefaultRoles.ORGANIZATION_SUPER_ADMIN
    ) {
      logging.logInfo(
        `User ${userid} granted access via default role: ${userRole}`,
      )
      return true
    }

    const rolePermissions = await rolePermissionHandler.getAPIListbyRole(
      userRole,
    )
    if (!rolePermissions || !rolePermissions.APIs) {
      logging.logInfo(
        `No role permissions found for user ${userid} with role ${userRole}`,
      )
      return false
    }

    const apiPermission = rolePermissions.APIs.find(
      (api) => api.api === apiName,
    )
    if (!apiPermission) {
      logging.logInfo(
        `No API permission found for ${apiName} for user ${userid} with role ${userRole}`,
      )
      return false
    }

    const hasMethodPermission = apiPermission.methods.includes(
      method.toUpperCase(),
    )
    logging.logInfo(
      `Method permission check for ${method} on ${apiName}: ${hasMethodPermission} (fallback role: ${userRole})`,
    )
    return hasMethodPermission
  } catch (error) {
    logging.logError(`Error in checkUserRole for ${userid}:`, error)
    return false
  }
}

module.exports = new ValidateUser()
